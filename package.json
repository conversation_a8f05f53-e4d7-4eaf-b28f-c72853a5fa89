{"name": "monorepo-frontend", "version": "1.0.0", "description": "", "main": "index.js", "private": true, "scripts": {"dev:management": "pnpm run -C apps/management-web dev", "dev:teacher": "pnpm run -C apps/teacher-web dev", "dev:qrcode": "pnpm run -C apps/qrcode-web dev", "dev:debug": "pnpm run -C apps/debug-web dev", "build:management": "pnpm run -C apps/management-web build", "build:teacher": "pnpm run -C apps/teacher-web build", "build:qrcode": "pnpm run -C apps/qrcode-web build", "build:debug": "pnpm run -C apps/debug-web build"}, "dependencies": {"@vueuse/core": "12.5.0", "axios": "1.7.9", "dayjs": "^1.11.13", "echarts": "^5.6.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "2.3.1", "sass": "1.83.4", "sortablejs": "1.15.6", "vue": "^3.5.13", "vue-echarts": "^7.0.3", "vue-router": "^4.5.0"}, "devDependencies": {"@arco-design/web-vue": "2.56.3", "@types/lodash": "^4.17.16", "@types/mockjs": "^1.0.9", "@types/node": "22.10.8", "@types/nprogress": "0.2.3", "@types/sortablejs": "1.15.8", "@vitejs/plugin-vue": "5.2.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "eslint": "^9.21.0", "eslint-plugin-vue": "~10.0.0", "mockjs": "^1.1.0", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "typescript": "5.7.3", "unplugin-auto-import": "19.0.0", "unplugin-vue-components": "28.0.0", "vite": "^6.2.1", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "2.2.0"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.5.2"}