import { type AppRouteRecordRaw } from '@/router/routes/types.ts'
import { DEFAULT_LAYOUT } from '@/router/routes/base.ts'

const CORRECTION: AppRouteRecordRaw = {
  path: '/users',
  name: 'Users',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '用户管理',
    icon: 'icon-user',
    order: 0,
  },
  children: [
    {
      path: 'users',
      name: 'UserList',
      component: async () => await import('@/views/user/list/index.vue'),
      meta: {
        locale: '用户列表',
      },
    },
  ],
}

export default CORRECTION
