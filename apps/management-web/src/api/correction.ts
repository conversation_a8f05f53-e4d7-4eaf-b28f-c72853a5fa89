import request from '@/utils/request'
import { PageResponse } from './type'

interface DashboardRequest {
  startTime: number
  endTime: number
}

export interface TaskRunStatus {
  taskId: string
  taskName: string
  count: number
  userCount: number
}

export interface DashboardResponse {
  expiredUserCount: number
  effectiveUserCount: number
  totalUserCount: number
  newUserCount: number
  taskRunStatus: TaskRunStatus[]
}

export function getDashboardData(data: DashboardRequest) {
  return request.get<DashboardResponse>('/api/v1/correction/dashboard', data)
}

interface UserListRequest {
  page: number
  size: number
}

export interface UserResponse {
  uuid: string
  wxNickName: string
  wxAvatarUrl: string
  createdAt: string
  vipTtl: string
}

export function getEffectiveUsers(req: UserListRequest) {
  return request.get<PageResponse<UserResponse>>('/api/v1/correction/users/effective', req)
}

export function getExpiredUsers(req: UserListRequest) {
  return request.get<PageResponse<UserResponse>>('/api/v1/correction/users/expired', req)
}

interface TaskListRequest {
  page: number
  size: number
}

export interface TaskResponse {
  runId: string
  taskId: string
  name: string
  taskName: string
  status: number
  createdAt: string
}

export function getTaskList(req: TaskListRequest) {
  return request.get<PageResponse<TaskResponse>>('/api/v1/correction/taskRuns', req)
}
