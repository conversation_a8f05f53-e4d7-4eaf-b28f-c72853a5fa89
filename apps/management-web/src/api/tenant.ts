import request from '@/utils/request'
import { PageResponse } from './type'

export interface GetTenantDetailResponse {
  tenantId: string
  tenantName: string
  tenantPoint: number
}

export function getTenantDetail(tenantId: string) {
  return request.get<GetTenantDetailResponse>(`/api/v1/tenants/detail`, {
    tenantId: tenantId,
  })
}

export enum TenantUserStatus {
  Pending = 'pending',
  Valid = 'valid',
}

interface GetTenantUsersRequest {
  tenantId: string
  page: number
  size: number
  status?: string
}

export interface GetTenantUsersResponse {
  avatar: string
  nickname: string
  phone: string
  roleId: string
  roleName: string
  status: string
  tenantId: string
  tenantUserId: string
  userId: string
}

export function getTenantUsers(req: GetTenantUsersRequest) {
  return request.get<PageResponse<GetTenantUsersResponse>>(`/api/v1/tenants/users`, req)
}

interface UpdateTenantUserStatusRequest {
  tenantUserId: string
  status: string
}

export function updateTenantUserStatus(req: UpdateTenantUserStatusRequest) {
  return request.post(`/api/v1/tenants/users/status`, req)
}

interface DeleteTenantUserRequest {
  tenantUserId: string
}

export function deleteTenantUser(req: DeleteTenantUserRequest) {
  return request.delete(`/api/v1/tenants/users`, req)
}
