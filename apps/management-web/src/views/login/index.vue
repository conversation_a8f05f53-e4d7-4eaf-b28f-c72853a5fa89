<template>
  <div class="container">
    <div class="content">
      <div class="title">登录 叮当批改管理端</div>
      <div class="sub-title">登录 叮当批改管理端</div>
      <div class="login-form">
        <a-tabs>
          <a-tab-pane
            key="1"
            title="密码登录"
          >
            <LoginForm />
          </a-tab-pane>
          <a-tab-pane
            key="2"
            title="验证码登录"
          >
            <CodeForm />
          </a-tab-pane>
        </a-tabs>
      </div>
      <div class="footer">
        <footer />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import CodeForm from './components/code-form.vue';
import LoginForm from './components/login-form.vue';
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.title {
  color: var(--color-text-1);
  font-weight: 500;
  font-size: 24px;
  line-height: 32px;
}

.sub-title {
  color: var(--color-text-3);
  font-size: 16px;
  line-height: 24px;
}

.login-form {
  width: 320px;
}
</style>
