<template>
  <div class="container">
    <a-card class="general-card">
      <a-row>
        <a-col :flex="1">
          <a-form
            :model="formModel"
            :label-col-props="{ span: 6 }"
            :wrapper-col-props="{ span: 18 }"
            label-align="left"
          >
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item
                  field="status"
                  label="状态"
                >
                  <a-select
                    v-model="formModel.status"
                    placeholder="请选择状态"
                    :options="[
                      { label: '待审核', value: TenantUserStatus.Pending },
                      { label: '正常', value: TenantUserStatus.Valid },
                    ]"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-col>
        <a-divider
          style="height: 84px"
          direction="vertical"
        />
        <a-col
          :flex="'86px'"
          style="text-align: right"
        >
          <a-space
            class="form-functions"
            direction="vertical"
            :size="18"
          >
            <a-button
              type="primary"
              @click="search"
            >
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
            <a-button @click="reset">
              <template #icon>
                <icon-refresh />
              </template>
              重置
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-divider style="margin-top: 0" />
      <a-row style="margin-bottom: 16px">
        <!-- <a-col :span="12">
          <a-space>
            <a-button type="primary" @click="handleAdd">
              <template #icon>
                <icon-plus />
              </template>
              新建用户
            </a-button>
          </a-space>
        </a-col> -->
        <a-col
          :span="24"
          style="
            display: flex;
            align-items: center;
            justify-content: end;
            height: 32px;
          "
        >
          <a-tooltip content="刷新">
            <div
              class="action-icon"
              @click="search"
            >
              <icon-refresh size="18" />
            </div>
          </a-tooltip>
        </a-col>
      </a-row>
      <a-table
        row-key="id"
        :loading="loading"
        :pagination="pagination"
        :columns="columns"
        :data="renderData"
        :bordered="false"
        size="medium"
        @page-change="onPageChange"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
        </template>
        <template #status="{ record }">
          <a-tag :color="record.status === TenantUserStatus.Pending ? 'red' : 'green'
            ">
            {{
              record.status === TenantUserStatus.Pending ? '待审核' : '正常'
            }}
          </a-tag>
        </template>
        <template #operations="{ record }">
          <a-button
            v-if="record.status === TenantUserStatus.Pending"
            type="text"
            size="small"
            @click="handleAgree(record.tenantUserId)"
          >
            同意
          </a-button>
          <a-button
            type="text"
            size="small"
            @click="handleDelete(record.tenantUserId)"
          >
            删除
          </a-button>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import useLoading from '@/hooks/loading.ts'
import { reactive, ref, computed } from 'vue'
import { Message, Modal, type TableColumnData } from '@arco-design/web-vue'
import { deleteTenantUser, getTenantUsers, GetTenantUsersResponse, TenantUserStatus, updateTenantUserStatus } from '@/api/tenant';
import { useUserStore } from '@/store';
import { storeToRefs } from 'pinia';

const userStore = useUserStore()
const { tenantId } = storeToRefs(userStore)
const { loading, setLoading } = useLoading(true)
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 0,
})
const renderData = ref<GetTenantUsersResponse[]>([])

const columns = computed<TableColumnData[]>(() => [
  {
    title: '序号',
    dataIndex: 'index',
    slotName: 'index',
  },
  {
    title: '手机号',
    dataIndex: 'phone',
  },
  {
    title: '角色',
    dataIndex: 'roleName',
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status',
  },
  {
    title: '操作',
    dataIndex: 'operations',
    slotName: 'operations',
  },
])

const generateFormModel = () => {
  return {
    status: 'valid',
  }
}
const formModel = ref(generateFormModel())

const fetchData = async (params = { page: 1, size: 20 }) => {
  setLoading(true)
  try {
    const data = await getTenantUsers({
      ...formModel.value,
      tenantId: tenantId!.value!,
      page: params.page,
      size: params.size,
    })
    renderData.value = data.data
    pagination.current = data.page
    pagination.total = data.total
  } catch (err) {
    console.log(err)
  } finally {
    setLoading(false)
  }
}

const onPageChange = (current: number) => {
  void fetchData({
    page: current,
    size: pagination.pageSize,
  })
}

const search = () => {
  void fetchData()
}

const reset = () => {
  formModel.value = generateFormModel()
}

const handleAgree = (tenantUserId: string) => {
  updateTenantUserStatus({
    tenantUserId,
    status: TenantUserStatus.Valid,
  })
    .then(() => {
      Message.success('更改成功')
      void fetchData()
    })
    .catch((err) => {
      console.error(err)
    })
}

const handleDelete = (tenantUserId: string) => {
  Modal.warning({
    title: '警告',
    content: `你正在删除用户，删除后不可恢复，是否继续？`,
    hideCancel: false,
    onOk() {
      deleteTenantUser({
        tenantUserId,
      })
        .then(() => {
          Message.success('删除成功')
          void fetchData()
        })
        .catch((err) => {
          console.error(err)
          Message.error('删除失败')
        })
    }
  })
}

void fetchData()
</script>

<script lang="ts">
export default {
  name: 'UserList',
}
</script>

<style scoped lang="scss">
.container {
  padding: 20px;
}

.action-icon {
  margin-left: 12px;
  cursor: pointer;
}

.form-functions {
  padding-bottom: 16px;
}

.setting {
  display: flex;
  align-items: center;
  width: 200px;

  .title {
    margin-left: 12px;
    cursor: pointer;
  }
}
</style>
