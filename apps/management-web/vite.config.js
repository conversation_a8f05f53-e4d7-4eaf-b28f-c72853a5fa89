var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
import { defineConfig, loadEnv } from 'vite';
import { join } from 'path';
import vue from '@vitejs/plugin-vue';
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import vueDevTools from 'vite-plugin-vue-devtools';
// https://vitejs.dev/config/
export default (function (_a) {
    var mode = _a.mode;
    process.env = __assign(__assign({}, process.env), loadEnv(mode, process.cwd()));
    return defineConfig({
        plugins: [
            vue(),
            vueDevTools(),
            AutoImport({
                resolvers: [],
            }),
            Components({
                resolvers: [],
            }),
        ],
        server: {
            proxy: {
                '/api/v1': {
                    target: process.env.VITE_API_URL,
                    changeOrigin: true,
                    secure: true,
                },
            },
        },
        resolve: {
            alias: {
                '@': join(__dirname, 'src'),
            },
        },
    });
});
