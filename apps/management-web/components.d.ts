/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Breadcrumb: typeof import('./src/components/breadcrumb/index.vue')['default']
    CountDownButton: typeof import('./src/components/count-down-button/index.vue')['default']
    FilterableTable: typeof import('./src/components/filterable-table/index.vue')['default']
    Footer: typeof import('./src/components/footer/index.vue')['default']
    List: typeof import('./src/components/message-box/list.vue')['default']
    Menu: typeof import('./src/components/menu/index.vue')['default']
    MessageBox: typeof import('./src/components/message-box/index.vue')['default']
    Navbar: typeof import('./src/components/navbar/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    TabBar: typeof import('./src/components/tab-bar/index.vue')['default']
    TabItem: typeof import('./src/components/tab-bar/tab-item.vue')['default']
  }
}
