# QRCode Web 项目

一个基于 uni-app + Vue3 + TypeScript + SCSS 的二维码扫描及批改任务绑定应用。

## 项目功能

- 二维码扫描
- 查看批改任务列表
- 查看任务详情
- 绑定任务到二维码

## API 接口

项目使用以下接口与后端通信：

- `GET /api/v1/corrections` - 获取批改任务列表
- `POST /api/v1/printer/scan` - 扫描二维码获取信息
- `POST /api/v1/printer/bind` - 绑定任务到二维码

## 项目结构

```
├── src
│   ├── api              // API接口
│   ├── components       // 公共组件
│   ├── pages            // 页面
│   │   ├── index        // 首页，显示任务列表
│   │   ├── scan         // 扫描二维码页面
│   │   ├── task         // 任务相关页面
│   │   │   ├── select   // 选择任务页面
│   │   │   └── detail   // 任务详情页面
│   │   └── bind         // 绑定相关页面
│   │       └── success  // 绑定成功页面
│   ├── static           // 静态资源
│   │   └── images       // 图片资源
│   ├── store            // 状态管理
│   ├── styles           // 全局样式
│   ├── utils            // 工具函数
│   ├── App.vue          // 应用入口组件
│   ├── main.ts          // 入口文件
│   ├── manifest.json    // 应用配置
│   ├── pages.json       // 页面配置
│   └── shims-uni.d.ts   // uni-app 类型声明
├── package.json         // 依赖管理
├── tsconfig.json        // TypeScript配置
├── vite.config.ts       // Vite 配置
└── README.md            // 项目说明
```

## 开发环境

- Node.js (>= 14.0.0)
- npm 或 yarn

## 运行项目

```bash
# 安装依赖
npm install

# 运行项目（H5模式）
npm run dev

# 打包项目
npm run build
```

## 功能流程

1. 首页显示批改任务列表
2. 用户可以查看任务详情
3. 用户扫描二维码
4. 选择批改任务进行绑定
5. 绑定成功后显示绑定信息

## 注意事项

- 二维码扫描功能需要在真机环境下测试
- 项目目前仅提供基础框架，具体功能需要根据实际业务进行开发 