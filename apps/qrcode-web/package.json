{"name": "qrcode-web", "version": "1.0.0", "description": "二维码扫描绑定项目", "main": "main.ts", "scripts": {"dev": "uni -p h5", "build": "uni build -p h5", "format": "prettier --write src/", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@dcloudio/uni-app": "vue3", "@dcloudio/uni-app-plus": "vue3", "@dcloudio/uni-components": "vue3", "@dcloudio/uni-h5": "vue3", "@dcloudio/uni-i18n": "vue3", "@dcloudio/uni-mp-alipay": "vue3", "@dcloudio/uni-mp-baidu": "vue3", "@dcloudio/uni-mp-kuaishou": "vue3", "@dcloudio/uni-mp-lark": "vue3", "@dcloudio/uni-mp-qq": "vue3", "@dcloudio/uni-mp-toutiao": "vue3", "@dcloudio/uni-mp-weixin": "vue3", "@dcloudio/uni-quickapp-webview": "vue3", "@vue/runtime-core": "^3.4.21", "@vue/shared": "^3.4.21", "pinia": "^2.0.28", "vue": "^3.4.21"}, "devDependencies": {"@dcloudio/types": "^3.0.0", "@dcloudio/uni-automator": "vue3", "@dcloudio/uni-cli-shared": "vue3", "@dcloudio/vite-plugin-uni": "vue3", "@types/node": "^18.11.18", "sass": "^1.57.1", "typescript": "^4.9.4", "vite": "^5.2.8"}}