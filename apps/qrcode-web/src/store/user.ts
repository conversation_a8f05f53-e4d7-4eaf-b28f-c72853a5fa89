import { defineStore } from 'pinia'
import { ref } from 'vue'
import { login as userLogin, logout as userLogout, getCurrentUser, type UserInfo, type LoginRequest } from '@/api'
import { setToken, clearToken, setUserInfo, getUserInfo as getStoredUserInfo, clearUserInfo, isLogin as checkIsLogin } from '@/utils/auth'

// 定义用户状态管理
export const useUserStore = defineStore('user', () => {
  const userInfo = ref<UserInfo | null>(getStoredUserInfo() || null)
  const loading = ref(false)
  const isLogin = ref(checkIsLogin())

  // 登录
  async function login(loginForm: LoginRequest) {
    loading.value = true
    try {
      const res = await userLogin(loginForm)
      setToken(res.accessToken)
      isLogin.value = true

      // 获取用户信息
      await getUserInfo()

      return res
    } catch (error) {
      clearToken()
      clearUserInfo()
      isLogin.value = false
      throw error
    } finally {
      loading.value = false
    }
  }

  // 获取用户信息
  async function getUserInfo() {
    if (!checkIsLogin()) {
      return null
    }

    loading.value = true
    try {
      const res = await getCurrentUser()
      userInfo.value = res
      setUserInfo(res)
      return res
    } catch (error) {
      console.error('获取用户信息失败', error)
      return null
    } finally {
      loading.value = false
    }
  }

  // 登出
  async function logout() {
    try {
      await userLogout()
    } catch (error) {
      console.error('登出失败', error)
    } finally {
      clearToken()
      clearUserInfo()
      userInfo.value = null
      isLogin.value = false
    }
  }

  // 检查登录状态
  function checkLoginStatus() {
    const loginStatus = checkIsLogin()
    isLogin.value = loginStatus
    return loginStatus
  }

  return {
    userInfo,
    loading,
    isLogin,
    login,
    getUserInfo,
    logout,
    checkLoginStatus
  }
})
