import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { CorrectionTask, PageResponse } from '@/api'
import { getCorrectionTasks } from '@/api'

export { useUserStore } from './user'

// 定义项目状态管理
export const useProjectStore = defineStore('project', () => {
  const projects = ref<CorrectionTask[]>([])
  const loading = ref(false)
  const currentQrCode = ref<string>('')
  const currentProject = ref<CorrectionTask | null>(null)
  const bindToken = ref<string>('')
  const bindExpireAt = ref<number>(0)

  // 获取项目列表
  async function fetchProjects(page: number = 0, size: number = 10) {
    loading.value = true
    try {
      const response = await getCorrectionTasks(page, size)
      projects.value = response.data
    } catch (error) {
      console.error('获取项目列表失败', error)
    } finally {
      loading.value = false
    }
  }

  // 设置当前二维码
  function setCurrentQrCode(code: string) {
    currentQrCode.value = code
  }

  // 设置当前选中的项目
  function setCurrentProject(project: CorrectionTask | null) {
    currentProject.value = project
  }

  // 设置绑定结果
  function setBindResult(token: string, expireAt: number) {
    bindToken.value = token
    bindExpireAt.value = expireAt
  }

  return {
    projects,
    loading,
    currentQrCode,
    currentProject,
    bindToken,
    bindExpireAt,
    fetchProjects,
    setCurrentQrCode,
    setCurrentProject,
    setBindResult
  }
})

// 定义批改任务状态管理
export const useCorrectionStore = defineStore('correction', () => {
  const tasks = ref<CorrectionTask[]>([])
  const loading = ref(false)
  const currentTask = ref<CorrectionTask | null>(null)
  const bindToken = ref<string>('')
  const bindExpireAt = ref<number>(0)

  // 获取批改任务列表
  async function fetchTasks(page: number = 0, size: number = 10) {
    loading.value = true
    try {
      const response = await getCorrectionTasks(page, size)
      tasks.value = response.data
    } catch (error) {
      console.error('获取批改任务列表失败', error)
    } finally {
      loading.value = false
    }
  }

  // 设置当前选中的任务
  function setCurrentTask(task: CorrectionTask | null) {
    currentTask.value = task
  }

  // 设置绑定结果
  function setBindResult(token: string, expireAt: number) {
    bindToken.value = token
    bindExpireAt.value = expireAt
  }

  // 清除绑定信息
  function clearBindInfo() {
    bindToken.value = ''
    bindExpireAt.value = 0
    currentTask.value = null
  }

  return {
    tasks,
    loading,
    currentTask,
    bindToken,
    bindExpireAt,
    fetchTasks,
    setCurrentTask,
    setBindResult,
    clearBindInfo
  }
})