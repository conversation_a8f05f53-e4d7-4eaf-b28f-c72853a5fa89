/// <reference types="@dcloudio/types" />
/// <reference types="node" />

// 扩展 uni 类型
interface UniNamespace {
  request(options: {
    url: string;
    method?: string;
    data?: any;
    header?: Record<string, string>;
    success?: (res: any) => void;
    fail?: (err: any) => void;
  }): void;
  navigateTo(options: { url: string }): void;
  navigateBack(options?: { delta?: number }): void;
  redirectTo(options: { url: string }): void;
  reLaunch(options: { url: string }): void;
  showToast(options: { title: string; icon?: string; duration?: number }): void;
  scanCode(options: {
    scanType?: string[];
    success?: (res: { result: string }) => void;
    fail?: (err: any) => void;
    complete?: () => void;
  }): void;
  getStorageSync(key: string): any;
  setStorageSync(key: string, data: any): void;
  removeStorageSync(key: string): void;
}

// 扩展全局变量
declare global {
  const uni: UniNamespace;
  function getCurrentPages(): any[];
}

export {}; 