// API 基础配置
const BASE_URL = import.meta.env.VITE_API_URL || '';
import { getAppVersion, getAppPlatform } from '@/utils/app-info';

// API响应类型
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}

// 用户登录请求
export interface LoginRequest {
  phone: string;
  password: string;
}

// 用户登录响应
export interface LoginResponse {
  accessToken: string;
  expireTime: number;
}

// 用户信息
export interface UserInfo {
  userId: number;
  username: string;
  nickname?: string;
  avatar?: string;
}

// 批改任务类型定义
export interface CorrectionTask {
  id: string;
  title: string;
  status: string;
  need_scan_paper: boolean;
  paper_with_answer: boolean;
  auto_correction: boolean;
  sheets_per_paper: number;
  double_sided: boolean;
  paper_file_ids: string[];
  answer_file_ids: string[];
  total_sheets: number;
  scanned_sheets: number;
  corrected_sheets: number;
  created_at: string;
  updated_at: string;
}

// 二维码扫描请求
export interface ScanPrinterRequest {
  qrCodeId: string;
}

// 二维码扫描响应
export interface ScanPrinterResponse {
  success: boolean;
}

// 任务绑定请求
export interface BindTaskRequest {
  qrCodeId: string;
  taskId: string;
}

// 任务绑定响应
export interface BindTaskResponse {
  token: string;
  expireAt: number;
}

// UNI-APP 请求响应类型
// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface UniResponse<T = any> {
  data: T;
  statusCode: number;
  header: Record<string, string>;
  cookies: string[];
}

// 请求封装
function request<T>(
  url: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
  data?: any
): Promise<T> {
  return new Promise((resolve, reject) => {
    uni.request({
      url: BASE_URL ? `${BASE_URL}${url}` : url,
      method,
      data,
      header: {
        'content-type': 'application/json',
        'Authorization': uni.getStorageSync('token') ? `Bearer ${uni.getStorageSync('token')}` : '',
        'x-lu-version': getAppVersion(),
        'x-lu-platform': getAppPlatform()
      },
      success: (res: any) => {
        if (res.statusCode !== 200) {
          uni.showToast({
            title: '服务器连接失败',
            icon: 'none'
          });
          reject(res);
          return;
        }

        const apiRes = res.data;
        if (apiRes.code !== 0) {
          // 处理登录过期
          if (apiRes.code === 20002) {
            uni.showToast({
              title: '登录已过期，请重新登录',
              icon: 'none'
            });
            setTimeout(() => {
              uni.redirectTo({
                url: '/pages/login/login'
              });
            }, 2000);
            reject(apiRes.message);
            return;
          }

          uni.showToast({
            title: apiRes.message || '请求失败',
            icon: 'none'
          });
          reject(apiRes.message);
          return;
        }

        resolve(apiRes.data);
      },
      fail: (err: any) => {
        uni.showToast({
          title: '网络错误',
          icon: 'none'
        });
        reject(err);
      }
    });
  });
}

// 分页响应接口
export interface PageResponse<T> {
  data: T[];
  size: number;
  page: number;
  total: number;
}

// 获取批改任务列表
export function getCorrectionTasks(page: number = 0, size: number = 10): Promise<PageResponse<CorrectionTask>> {
  return request<PageResponse<CorrectionTask>>(`/api/v1/corrections?page=${page}&size=${size}`);
}

// 扫描二维码获取二维码信息
export function scanPrinter(qrCodeId: string): Promise<ScanPrinterResponse> {
  return request<ScanPrinterResponse>('/api/v1/printer/scan', 'POST', {
    qr_code_id: qrCodeId
  });
}

// 绑定任务到二维码
export function bindTask(qrCodeId: string, taskId: string): Promise<BindTaskResponse> {
  return request<BindTaskResponse>('/api/v1/printer/bind', 'POST', {
    qr_code_id: qrCodeId,
    task_id: taskId
  });
}

// 用户登录
export function login(data: LoginRequest): Promise<LoginResponse> {
  return request<LoginResponse>('/api/v1/auth/phone/loginByPassword', 'POST', data);
}

// 用户登出
export function logout(): Promise<any> {
  return request<any>('/api/v1/auth/logout', 'POST');
}

// 获取当前用户信息
export function getCurrentUser(): Promise<UserInfo> {
  return request<UserInfo>('/api/v1/auth/current');
}
