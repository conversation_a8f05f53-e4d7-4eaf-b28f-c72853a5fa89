<script setup lang="ts">
import { ref } from 'vue'

interface Props {
  scanCallback?: (result: string) => void
  errorCallback?: (error: any) => void
}

const props = withDefaults(defineProps<Props>(), {
  scanCallback: () => {},
  errorCallback: () => {}
})

const scanning = ref(false)

// 开始扫描
const startScan = () => {
  scanning.value = true
  
  uni.scanCode({
    scanType: ['qrCode'],
    success: (res) => {
      props.scanCallback(res.result)
    },
    fail: (err) => {
      props.errorCallback(err)
    },
    complete: () => {
      scanning.value = false
    }
  })
}
</script>

<template>
  <view class="qr-scanner">
    <view class="scan-area">
      <view class="scan-box">
        <view class="scan-line" v-if="scanning"></view>
        <view class="scan-corners">
          <view class="corner top-left"></view>
          <view class="corner top-right"></view>
          <view class="corner bottom-left"></view>
          <view class="corner bottom-right"></view>
        </view>
      </view>
    </view>
    
    <slot name="before-button"></slot>
    
    <button class="scan-button" @click="startScan" :disabled="scanning">
      <slot name="button-text">
        {{ scanning ? '扫描中...' : '点击扫描' }}
      </slot>
    </button>
    
    <slot name="after-button"></slot>
  </view>
</template>

<style lang="scss" scoped>
.qr-scanner {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  .scan-area {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40rpx 0;
    
    .scan-box {
      width: 500rpx;
      height: 500rpx;
      position: relative;
      background-color: rgba(0, 0, 0, 0.03);
      
      .scan-line {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 4rpx;
        background-color: #3cc51f;
        box-shadow: 0 0 8rpx rgba(60, 197, 31, 0.8);
        animation: scanAnimation 2s linear infinite;
      }
      
      .scan-corners {
        position: absolute;
        width: 100%;
        height: 100%;
        
        .corner {
          position: absolute;
          width: 60rpx;
          height: 60rpx;
          border-color: #3cc51f;
          
          &.top-left {
            top: 0;
            left: 0;
            border-top: 6rpx solid;
            border-left: 6rpx solid;
          }
          
          &.top-right {
            top: 0;
            right: 0;
            border-top: 6rpx solid;
            border-right: 6rpx solid;
          }
          
          &.bottom-left {
            bottom: 0;
            left: 0;
            border-bottom: 6rpx solid;
            border-left: 6rpx solid;
          }
          
          &.bottom-right {
            bottom: 0;
            right: 0;
            border-bottom: 6rpx solid;
            border-right: 6rpx solid;
          }
        }
      }
    }
  }
  
  .scan-button {
    background-color: #3cc51f;
    color: #fff;
    width: 400rpx;
    height: 88rpx;
    line-height: 88rpx;
    border-radius: 44rpx;
    font-size: 30rpx;
    margin-top: 60rpx;
    
    &:disabled {
      background-color: #ccc;
    }
  }
}

@keyframes scanAnimation {
  0% {
    top: 0;
  }
  50% {
    top: 496rpx;
  }
  100% {
    top: 0;
  }
}
</style>