<script setup lang="ts">
import { computed } from 'vue'
import type { Project } from '@/api'
import { formatDate } from '@/utils'

interface Props {
  project: Project
  selected?: boolean
  selectable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  selected: false,
  selectable: false
})

const emit = defineEmits(['select', 'click'])

// 格式化日期
const formattedDate = computed(() => {
  return formatDate(props.project.createdAt)
})

// 选择项目
const handleClick = () => {
  emit('click', props.project)
  if (props.selectable) {
    emit('select', props.project)
  }
}
</script>

<template>
  <view 
    class="project-card" 
    :class="{ 'selectable': selectable, 'selected': selected }"
    @click="handleClick"
  >
    <view class="project-info">
      <view class="project-name">{{ project.name }}</view>
      <view class="project-desc">{{ project.description }}</view>
      <view class="project-time">创建时间: {{ formattedDate }}</view>
    </view>
    
    <view class="select-icon" v-if="selected && selectable">
      <text>✓</text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.project-card {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  
  &.selectable {
    cursor: pointer;
    
    &:active {
      background-color: #f8f8f8;
    }
  }
  
  &.selected {
    border: 2rpx solid #3cc51f;
    background-color: rgba(60, 197, 31, 0.05);
  }
  
  .project-info {
    flex: 1;
    
    .project-name {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 10rpx;
    }
    
    .project-desc {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 10rpx;
    }
    
    .project-time {
      font-size: 24rpx;
      color: #999;
    }
  }
  
  .select-icon {
    width: 60rpx;
    height: 60rpx;
    background-color: #3cc51f;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 32rpx;
  }
}
</style> 