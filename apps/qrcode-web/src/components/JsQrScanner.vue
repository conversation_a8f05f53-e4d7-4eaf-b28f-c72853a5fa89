<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 定义组件属性
interface Props {
  // 扫描成功回调
  onSuccess?: (decodedText: string) => void
  // 扫描失败回调
  onError?: (errorMessage: string) => void
  // 扫描框宽度
  width?: number
  // 扫描框高度
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  onSuccess: () => {},
  onError: () => {},
  width: 300,
  height: 300
})

// 组件状态
const scanning = ref(false)
const fileInput = ref<HTMLInputElement | null>(null)
const scriptLoaded = ref(false)

// 加载jsQR库
const loadJsQrScript = () => {
  return new Promise<void>((resolve, reject) => {
    // 检查是否已加载
    if (window.jsQR) {
      scriptLoaded.value = true
      resolve()
      return
    }

    // 创建script标签
    const script = document.createElement('script')
    script.src = 'https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js'
    script.async = true
    script.onload = () => {
      scriptLoaded.value = true
      resolve()
    }
    script.onerror = () => {
      reject(new Error('Failed to load jsQR script'))
    }

    // 添加到文档中
    document.head.appendChild(script)
  })
}

// 从文件中扫描二维码
const scanFromFile = async (event: Event) => {
  const target = event.target as HTMLInputElement
  if (!target.files || !target.files.length) return
  
  const file = target.files[0]
  scanning.value = true
  
  try {
    // 确保脚本已加载
    if (!scriptLoaded.value) {
      await loadJsQrScript()
    }
    
    // 读取文件
    const imageUrl = URL.createObjectURL(file)
    const image = new Image()
    
    image.onload = () => {
      // 创建canvas并绘制图像
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')
      
      if (!context) {
        props.onError('浏览器不支持Canvas')
        scanning.value = false
        return
      }
      
      // 设置canvas大小
      canvas.width = image.width
      canvas.height = image.height
      
      // 绘制图像
      context.drawImage(image, 0, 0, image.width, image.height)
      
      // 获取图像数据
      const imageData = context.getImageData(0, 0, image.width, image.height)
      
      // 使用jsQR解析二维码
      const code = window.jsQR(imageData.data, imageData.width, imageData.height, {
        inversionAttempts: 'dontInvert'
      })
      
      if (code) {
        // 扫描成功
        props.onSuccess(code.data)
      } else {
        // 未找到二维码
        props.onError('未在图片中找到有效的二维码')
      }
      
      // 释放资源
      URL.revokeObjectURL(imageUrl)
      scanning.value = false
    }
    
    image.onerror = () => {
      props.onError('图片加载失败')
      URL.revokeObjectURL(imageUrl)
      scanning.value = false
    }
    
    // 加载图片
    image.src = imageUrl
    
  } catch (error: any) {
    console.error('从文件扫描二维码失败:', error)
    props.onError(error.toString() || '无法从文件中识别二维码')
    scanning.value = false
  }
}

// 组件挂载时
onMounted(async () => {
  try {
    await loadJsQrScript()
  } catch (error) {
    console.error('初始化jsQR扫描器失败:', error)
  }
})

// 为TypeScript声明全局类型
declare global {
  interface Window {
    jsQR: any;
  }
}

// 暴露方法给父组件
defineExpose({
  scanFromFile
})
</script>

<template>
  <div class="js-qr-scanner">
    <!-- 文件上传选项 -->
    <div class="file-upload">
      <input 
        ref="fileInput"
        type="file"
        accept="image/*"
        @change="scanFromFile"
        class="file-input"
      />
      <button class="upload-btn" @click="() => fileInput?.click()" :disabled="scanning">
        {{ scanning ? '处理中...' : '从相册选择二维码' }}
      </button>
    </div>
    
    <!-- 提示信息 -->
    <div class="scanner-tips">
      <p>请选择包含二维码的图片</p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.js-qr-scanner {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  
  .file-upload {
    margin: 30rpx 0;
    
    .file-input {
      display: none;
    }
    
    .upload-btn {
      background-color: #1989fa;
      color: #fff;
      border: none;
      border-radius: 44rpx;
      padding: 20rpx 40rpx;
      font-size: 28rpx;
      
      &:disabled {
        background-color: #ccc;
      }
    }
  }
  
  .scanner-tips {
    margin: 20rpx 0;
    color: #666;
    text-align: center;
    font-size: 28rpx;
  }
}
</style>
