<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 定义组件属性
interface Props {
  // 扫描成功回调
  onSuccess?: (decodedText: string) => void
  // 扫描失败回调
  onError?: (errorMessage: string) => void
  // 是否自动开始扫描
  autoStart?: boolean
  // 扫描框宽度
  width?: number
  // 扫描框高度
  height?: number
}

const props = withDefaults(defineProps<Props>(), {
  onSuccess: () => {},
  onError: () => {},
  autoStart: false,
  width: 300,
  height: 300
})

// 组件状态
const scanning = ref(false)
const hasCamera = ref(false)
const cameraError = ref(false)
const errorMessage = ref('')
const scannerContainer = ref<HTMLElement | null>(null)
const fileInput = ref<HTMLInputElement | null>(null)
const html5QrCode = ref<any>(null)
const scriptLoaded = ref(false)

// 加载HTML5-QRCode库
const loadHtml5QrCodeScript = () => {
  return new Promise<void>((resolve, reject) => {
    // 检查是否已加载
    if (window.Html5Qrcode) {
      scriptLoaded.value = true
      resolve()
      return
    }

    // 创建script标签
    const script = document.createElement('script')
    script.src = 'https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js'
    script.async = true
    script.onload = () => {
      scriptLoaded.value = true
      resolve()
    }
    script.onerror = (error) => {
      reject(new Error('Failed to load html5-qrcode script'))
    }

    // 添加到文档中
    document.head.appendChild(script)
  })
}

// 检查是否支持摄像头
const checkCameraSupport = async () => {
  try {
    const devices = await navigator.mediaDevices.enumerateDevices()
    const videoDevices = devices.filter(device => device.kind === 'videoinput')
    hasCamera.value = videoDevices.length > 0
    return hasCamera.value
  } catch (error) {
    console.error('检查摄像头支持失败:', error)
    hasCamera.value = false
    return false
  }
}

// 开始扫描
const startScanning = async () => {
  if (!scannerContainer.value) return

  try {
    scanning.value = true
    cameraError.value = false
    errorMessage.value = ''

    // 确保脚本已加载
    if (!scriptLoaded.value) {
      await loadHtml5QrCodeScript()
    }

    // 创建扫描器实例
    html5QrCode.value = new window.Html5Qrcode('qr-reader')

    const hasCameraSupport = await checkCameraSupport()

    if (hasCameraSupport) {
      const cameras = await window.Html5Qrcode.getCameras()
      if (cameras && cameras.length > 0) {
        const cameraId = cameras[0].id

        await html5QrCode.value.start(
          cameraId,
          {
            fps: 10,
            qrbox: { width: props.width * 0.8, height: props.height * 0.8 }
          },
          (decodedText: string) => {
            // 扫描成功
            props.onSuccess(decodedText)
            stopScanning()
          },
          (errorMessage: string) => {
            // 处理扫描过程中的错误
            console.log(`QR扫描过程中的错误: ${errorMessage}`)
          }
        )
      } else {
        cameraError.value = true
        errorMessage.value = '未检测到摄像头设备'
        scanning.value = false
      }
    } else {
      cameraError.value = true
      errorMessage.value = '您的设备不支持摄像头或未授权访问'
      scanning.value = false
    }
  } catch (error: any) {
    console.error('启动扫描器失败:', error)
    cameraError.value = true
    errorMessage.value = error.toString() || '启动扫描器失败'
    scanning.value = false
  }
}

// 停止扫描
const stopScanning = async () => {
  if (html5QrCode.value && html5QrCode.value.isScanning) {
    try {
      await html5QrCode.value.stop()
      console.log('QR扫描已停止')
    } catch (error) {
      console.error('停止扫描失败:', error)
    }
  }
  scanning.value = false
}

// 从文件中扫描二维码
const scanFromFile = async (event: Event) => {
  const target = event.target as HTMLInputElement
  if (!target.files || !target.files.length) return

  const file = target.files[0]

  try {
    // 确保脚本已加载
    if (!scriptLoaded.value) {
      await loadHtml5QrCodeScript()
    }

    if (!html5QrCode.value) {
      html5QrCode.value = new window.Html5Qrcode('qr-reader')
    }

    const result = await html5QrCode.value.scanFile(file, true)
    props.onSuccess(result)
  } catch (error: any) {
    console.error('从文件扫描二维码失败:', error)
    props.onError(error.toString() || '无法从文件中识别二维码')
  }
}

// 组件挂载时
onMounted(async () => {
  try {
    await loadHtml5QrCodeScript()
    await checkCameraSupport()

    if (props.autoStart && hasCamera.value) {
      startScanning()
    }
  } catch (error) {
    console.error('初始化QR扫描器失败:', error)
    cameraError.value = true
    errorMessage.value = '加载扫描组件失败，请刷新页面重试'
  }
})

// 组件卸载时
onUnmounted(() => {
  stopScanning()
})

// 为TypeScript声明全局类型
declare global {
  interface Window {
    Html5Qrcode: any;
  }
}

// 暴露方法给父组件
defineExpose({
  startScanning,
  stopScanning,
  scanFromFile
})
</script>

<template>
  <div class="html5-qr-scanner">
    <!-- 扫描器容器 -->
    <div ref="scannerContainer" id="qr-reader" :style="{ width: `${width}px`, height: `${height}px` }"></div>

    <!-- 错误提示 -->
    <div v-if="cameraError" class="scanner-error">
      <p>{{ errorMessage }}</p>
      <p>您可以尝试从相册选择二维码图片</p>
    </div>

    <!-- 文件上传选项 -->
    <div class="file-upload">
      <input
        ref="fileInput"
        type="file"
        accept="image/*"
        @change="scanFromFile"
        class="file-input"
      />
      <button class="upload-btn" @click="() => fileInput?.click()">
        从相册选择二维码
      </button>
    </div>

    <!-- 扫描按钮 -->
    <div v-if="hasCamera && !scanning" class="scanner-actions">
      <button class="scan-btn" @click="startScanning">开始扫描</button>
    </div>

    <!-- 停止扫描按钮 -->
    <div v-if="scanning" class="scanner-actions">
      <button class="stop-btn" @click="stopScanning">停止扫描</button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.html5-qr-scanner {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  #qr-reader {
    width: 300px;
    height: 300px;
    margin: 0 auto;
    position: relative;

    // 覆盖库的默认样式
    ::v-deep(video) {
      width: 100% !important;
      height: 100% !important;
      object-fit: cover;
    }

    ::v-deep(img) {
      max-width: 100%;
      max-height: 100%;
    }
  }

  .scanner-error {
    margin: 20rpx 0;
    color: #ff4d4f;
    text-align: center;
    font-size: 28rpx;
  }

  .file-upload {
    margin: 30rpx 0;

    .file-input {
      display: none;
    }

    .upload-btn {
      background-color: #1989fa;
      color: #fff;
      border: none;
      border-radius: 44rpx;
      padding: 20rpx 40rpx;
      font-size: 28rpx;
    }
  }

  .scanner-actions {
    margin: 30rpx 0;

    .scan-btn, .stop-btn {
      background-color: #3cc51f;
      color: #fff;
      border: none;
      border-radius: 44rpx;
      padding: 20rpx 40rpx;
      font-size: 28rpx;
    }

    .stop-btn {
      background-color: #ff4d4f;
    }
  }
}
</style>
