/**
 * 认证相关工具函数
 */

const TOKEN_KEY = 'token';
const USER_INFO_KEY = 'user_info';
const SELECTED_PROJECT_KEY = 'selected_project';

/**
 * 检查是否已登录
 * @returns 是否已登录
 */
export function isLogin(): boolean {
  return !!getToken();
}

/**
 * 获取存储的token
 * @returns token字符串或空字符串
 */
export function getToken(): string {
  return uni.getStorageSync(TOKEN_KEY) || '';
}

/**
 * 保存token到本地存储
 * @param token 认证token
 */
export function setToken(token: string): void {
  uni.setStorageSync(TOKEN_KEY, token);
}

/**
 * 清除token
 */
export function clearToken(): void {
  uni.removeStorageSync(TOKEN_KEY);
}

/**
 * 保存用户信息
 * @param userInfo 用户信息对象
 */
export function setUserInfo(userInfo: any): void {
  uni.setStorageSync(USER_INFO_KEY, JSON.stringify(userInfo));
}

/**
 * 获取用户信息
 * @returns 用户信息对象
 */
export function getUserInfo(): any {
  const userInfoStr = uni.getStorageSync(USER_INFO_KEY);
  return userInfoStr ? JSON.parse(userInfoStr) : null;
}

/**
 * 清除用户信息
 */
export function clearUserInfo(): void {
  uni.removeStorageSync(USER_INFO_KEY);
}

/**
 * 保存选中的项目
 * @param project 项目信息
 */
export function setSelectedProject(project: any): void {
  uni.setStorageSync(SELECTED_PROJECT_KEY, JSON.stringify(project));
}

/**
 * 获取选中的项目
 * @returns 项目信息
 */
export function getSelectedProject(): any {
  const projectStr = uni.getStorageSync(SELECTED_PROJECT_KEY);
  return projectStr ? JSON.parse(projectStr) : null;
}

/**
 * 清除选中的项目
 */
export function clearSelectedProject(): void {
  uni.removeStorageSync(SELECTED_PROJECT_KEY);
}

/**
 * 清除所有认证相关数据
 */
export function clearAuthData(): void {
  clearToken();
  clearUserInfo();
  clearSelectedProject();
}
