/**
 * 日期格式化
 * @param date 日期对象或时间戳
 * @param format 格式化模板，如 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export function formatDate(date: Date | number | string, format: string = 'YYYY-MM-DD'): string {
  const d = new Date(date);
  
  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const day = d.getDate();
  const hour = d.getHours();
  const minute = d.getMinutes();
  const second = d.getSeconds();
  
  return format
    .replace(/YYYY/g, year.toString())
    .replace(/MM/g, month.toString().padStart(2, '0'))
    .replace(/DD/g, day.toString().padStart(2, '0'))
    .replace(/HH/g, hour.toString().padStart(2, '0'))
    .replace(/mm/g, minute.toString().padStart(2, '0'))
    .replace(/ss/g, second.toString().padStart(2, '0'));
}

/**
 * 存储数据到本地
 * @param key 键名
 * @param data 数据内容
 */
export function setStorage(key: string, data: any): void {
  try {
    uni.setStorageSync(key, JSON.stringify(data));
  } catch (e) {
    console.error('数据存储失败', e);
  }
}

/**
 * 获取本地存储数据
 * @param key 键名
 * @param defaultValue 默认值
 * @returns 存储的数据或默认值
 */
export function getStorage<T>(key: string, defaultValue: T): T {
  try {
    const value = uni.getStorageSync(key);
    return value ? JSON.parse(value) : defaultValue;
  } catch (e) {
    console.error('获取存储数据失败', e);
    return defaultValue;
  }
}

/**
 * 移除本地存储数据
 * @param key 键名
 */
export function removeStorage(key: string): void {
  try {
    uni.removeStorageSync(key);
  } catch (e) {
    console.error('移除存储数据失败', e);
  }
}

/**
 * 防抖函数
 * @param fn 需要防抖的函数
 * @param delay 延迟时间，单位毫秒
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number = 300
): (...args: Parameters<T>) => void {
  let timer: number | null = null;
  
  return function(this: any, ...args: Parameters<T>): void {
    if (timer) {
      clearTimeout(timer);
    }
    
    timer = setTimeout(() => {
      fn.apply(this, args);
      timer = null;
    }, delay) as unknown as number;
  };
} 