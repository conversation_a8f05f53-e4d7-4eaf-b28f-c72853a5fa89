<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useProjectStore, useCorrectionStore, useUserStore } from '@/store'
import { scanPrinter, bindTask } from '@/api'
import { getSelectedProject } from '@/utils/auth'
import Html5QrScanner from '@/components/Html5QrScanner.vue'
import JsQrScanner from '@/components/JsQrScanner.vue'

const projectStore = useProjectStore()
const correctionStore = useCorrectionStore()
const userStore = useUserStore()
const scanResult = ref('')
const scanning = ref(false)
const scanSuccess = ref(false)
const scanError = ref(false)
const errorMessage = ref('')
const qrScanner = ref()
const taskId = ref('')
const fromDetail = ref(false)
const binding = ref(false)

// 获取页面参数
const getPageParams = () => {
  const pages = getCurrentPages()
  const page = pages[pages.length - 1]
  // @ts-ignore
  const options = page?.options || {}
  return options
}

// 检查登录状态和项目选择
onMounted(() => {
  // 检查登录状态
  if (!userStore.checkLoginStatus()) {
    uni.redirectTo({
      url: '/pages/login/login'
    })
    return
  }

  // 获取页面参数
  const params = getPageParams()
  taskId.value = params.taskId || ''
  fromDetail.value = params.fromDetail === 'true'

  // 如果是从详情页跳转来的，则不需要检查项目选择
  if (fromDetail.value && taskId.value) {
    // 从详情页跳转来，已有任务ID，直接使用
    return
  }

  // 检查是否已选择项目
  const selectedProject = getSelectedProject()
  if (selectedProject) {
    projectStore.setCurrentProject(selectedProject)
  } else {
    uni.redirectTo({
      url: '/pages/project/select'
    })
  }
})

// 处理扫描成功
const handleScanSuccess = async (decodedText: string) => {
  scanResult.value = decodedText
  scanning.value = true
  scanSuccess.value = false
  scanError.value = false

  try {
    // 获取二维码相关信息
    const response = await scanPrinter(decodedText)
    if (response.success) {
      // 保存二维码信息到全局状态
      projectStore.setCurrentQrCode(decodedText)
      scanSuccess.value = true

      // 如果是从详情页跳转来的，直接绑定
      if (fromDetail.value && taskId.value) {
        // 直接绑定任务
        await handleDirectBind(decodedText, taskId.value)
      } else {
        // 跳转到选择任务页面
        setTimeout(() => {
          uni.navigateTo({
            url: '/pages/task/select'
          })
        }, 1500)
      }
    } else {
      scanError.value = true
      errorMessage.value = response.message || '无法识别该二维码或二维码已失效'
    }
  } catch (error) {
    scanError.value = true
    errorMessage.value = '无法识别该二维码或二维码已失效'
    console.error('扫描二维码出错', error)
  } finally {
    scanning.value = false
  }
}

// 直接绑定任务
const handleDirectBind = async (qrCodeId: string, taskId: string) => {
  binding.value = true
  try {
    const response = await bindTask(qrCodeId, taskId)

    // 保存绑定结果
    correctionStore.setBindResult(response.token, response.expireAt)

    // 显示成功提示
    uni.showToast({
      title: '绑定成功',
      icon: 'success'
    })

    // 跳转到首页
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/index/index'
      })
    }, 1500)
  } catch (error) {
    uni.showToast({
      title: '绑定失败，请重试',
      icon: 'none'
    })
    console.error('绑定任务失败', error)
  } finally {
    binding.value = false
  }
}

// 处理扫描错误
const handleScanError = (error: string) => {
  scanning.value = false
  scanError.value = true
  errorMessage.value = error || '扫描失败，请重试'
  console.error('扫描失败', error)
}

// 开始扫描 - 兼容非H5环境
const startScan = () => {
  // 检查是否在H5环境
  // #ifdef H5
  if (qrScanner.value) {
    qrScanner.value.startScanning()
  }
  // #endif

  // 非H5环境使用uni.scanCode
  // #ifndef H5
  scanning.value = true
  scanSuccess.value = false
  scanError.value = false

  uni.scanCode({
    scanType: ['qrCode'],
    success: async (res) => {
      handleScanSuccess(res.result)
    },
    fail: (err) => {
      handleScanError(err.errMsg || '扫描失败')
    }
  })
  // #endif
}

// 返回首页
const goBack = () => {
  uni.navigateBack()
}
</script>

<template>
  <view class="page-container scan-page">
    <view class="scan-header">
      <text class="title">扫描二维码</text>
      <text class="subtitle">请将二维码对准扫描框</text>
    </view>

    <!-- H5环境使用HTML5 QR扫描器 -->
    <!-- #ifdef H5 -->
    <view class="scan-area h5-scan-area">
      <Html5QrScanner
        ref="qrScanner"
        :width="300"
        :height="300"
        :onSuccess="handleScanSuccess"
        :onError="handleScanError"
      />
    </view>

    <view class="scan-divider">
      <text>或者</text>
    </view>

    <view class="scan-area h5-scan-area">
      <JsQrScanner
        :width="300"
        :height="300"
        :onSuccess="handleScanSuccess"
        :onError="handleScanError"
      />
    </view>
    <!-- #endif -->

    <!-- 非H5环境显示扫描框 -->
    <!-- #ifndef H5 -->
    <view class="scan-area">
      <view class="scan-box">
        <view class="scan-animation" v-if="scanning"></view>
      </view>
    </view>

    <view class="action-buttons">
      <button class="btn btn-primary" @click="startScan" :disabled="scanning">
        {{ scanning ? '扫描中...' : '开始扫描' }}
      </button>
    </view>
    <!-- #endif -->

    <view class="scan-result" v-if="scanSuccess">
      <view class="success-icon">✓</view>
      <text class="success-text">扫描成功，正在跳转...</text>
    </view>

    <view class="scan-result" v-if="scanError">
      <view class="error-icon">!</view>
      <text class="error-text">{{ errorMessage }}</text>
    </view>

    <view class="action-buttons">
      <button class="btn" @click="goBack">返回</button>
    </view>
  </view>
</template>

<style lang="scss">
.scan-page {

  .scan-divider {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20rpx 0;
    width: 80%;

    &::before,
    &::after {
      content: '';
      flex: 1;
      height: 1px;
      background-color: #ddd;
    }

    text {
      padding: 0 20rpx;
      color: #999;
      font-size: 28rpx;
    }
  }
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100vh;

  .scan-header {
    margin: 60rpx 0;
    text-align: center;

    .title {
      font-size: 40rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
      display: block;
    }

    .subtitle {
      font-size: 28rpx;
      color: #666;
    }
  }

  .scan-area {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;

    &.h5-scan-area {
      margin: 20rpx 0;
    }

    .scan-box {
      width: 500rpx;
      height: 500rpx;
      background: rgba(0, 0, 0, 0.05);
      position: relative;
      border: 2rpx solid #3cc51f;

      .scan-animation {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 6rpx;
        background: #3cc51f;
        animation: scanAnimation 2s linear infinite;
      }
    }
  }

  .scan-result {
    margin: 30rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;

    .success-icon, .error-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 40rpx;
      margin-bottom: 20rpx;
    }

    .success-icon {
      background-color: #3cc51f;
      color: #fff;
    }

    .error-icon {
      background-color: #ff4d4f;
      color: #fff;
    }

    .success-text {
      color: #3cc51f;
      font-size: 28rpx;
    }

    .error-text {
      color: #ff4d4f;
      font-size: 28rpx;
    }
  }

  .action-buttons {
    margin: 60rpx 0;
    display: flex;
    flex-direction: column;
    width: 80%;

    .btn {
      margin-bottom: 20rpx;
    }
  }
}

@keyframes scanAnimation {
  0% {
    top: 0;
  }
  50% {
    top: 494rpx;
  }
  100% {
    top: 0;
  }
}
</style>