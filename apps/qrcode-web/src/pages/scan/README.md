# QR Code Scanning Implementation

This directory contains the implementation of QR code scanning functionality for the qrcode-web app.

## Implementation Details

The QR code scanning feature is implemented with multiple approaches to ensure compatibility across different platforms:

1. **Native Scanning (Non-H5 Environments)**
   - Uses `uni.scanCode()` API for native platforms (WeChat Mini Program, App, etc.)
   - Provides the best performance and user experience on native platforms

2. **HTML5 Camera Scanning (H5 Environment)**
   - Uses the `html5-qrcode` library loaded from CDN
   - Provides real-time camera scanning in browsers that support camera access
   - Requires user permission to access the camera

3. **Image Upload Scanning (H5 Environment)**
   - Uses the `jsQR` library loaded from CDN
   - Allows users to upload images containing QR codes
   - Serves as a fallback when camera access is not available or denied

## Components

- `Html5QrScanner.vue`: Camera-based QR code scanning for H5 environments
- `JsQrScanner.vue`: Image upload-based QR code scanning for H5 environments
- `scan.vue`: Main scanning page that conditionally renders the appropriate scanner

## Usage Notes

1. The scanning page automatically checks if the user is logged in and has selected a project
2. After successful scanning, the QR code data is sent to the server for validation
3. If the QR code is valid, the user is redirected to the task selection page

## Troubleshooting

- If camera access is denied, the user can still use the image upload option
- If scanning fails, clear error messages are displayed to guide the user
- For persistent issues, users can return to the previous page and try again

## Dependencies

- `html5-qrcode`: Loaded from CDN (https://unpkg.com/html5-qrcode@2.3.8/html5-qrcode.min.js)
- `jsQR`: Loaded from CDN (https://cdn.jsdelivr.net/npm/jsqr@1.4.0/dist/jsQR.min.js)
