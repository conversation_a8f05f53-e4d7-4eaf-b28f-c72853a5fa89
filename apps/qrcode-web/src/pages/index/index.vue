<script setup lang="ts">
import { onMounted } from 'vue'
import { useProjectStore, useCorrectionStore, useUserStore } from '@/store'
import { clearSelectedProject } from '@/utils/auth'

const projectStore = useProjectStore()
const correctionStore = useCorrectionStore()
const userStore = useUserStore()

// 扫描二维码
const scanQrCode = () => {
  // 检查登录状态
  if (!userStore.checkLoginStatus()) {
    uni.navigateTo({
      url: '/pages/login/login'
    })
    return
  }

  // 已登录，直接跳转到扫描页面
  // 扫描成功后再选择项目
  uni.navigateTo({
    url: '/pages/scan/scan'
  })
}

// 查看任务详情
const viewTaskDetail = (taskId: string) => {
  uni.navigateTo({
    url: `/pages/task/detail?id=${taskId}`
  })
}

// 登录页面
const goToLogin = () => {
  uni.navigateTo({
    url: '/pages/login/login'
  })
}

onMounted(() => {
  // 清除之前选择的项目
  clearSelectedProject()

  // 检查登录状态
  userStore.checkLoginStatus()

  // 如果已登录，加载批改任务列表
  if (userStore.isLogin) {
    correctionStore.fetchTasks(0, 10)
  }
})
</script>

<template>
  <view class="page-container">
    <view class="welcome-block">
      <text class="title">叮当全科批改</text>
      <text class="desc" v-if="userStore.isLogin">扫描二维码并选择要绑定的批改任务</text>
    </view>

    <view class="btn-block" v-if="userStore.isLogin">
      <button class="btn btn-primary" @click="scanQrCode">扫描二维码</button>
    </view>

    <view class="btn-block" v-else>
      <button class="btn btn-primary" @click="goToLogin">登录</button>
    </view>

    <view class="task-list" v-if="userStore.isLogin && correctionStore.tasks.length > 0">
      <view class="list-header">
        <text>批改任务列表</text>
      </view>
      <view
        class="card task-card"
        v-for="task in correctionStore.tasks"
        :key="task.id"
        @click="viewTaskDetail(task.id)"
      >
        <view class="task-title">{{ task.title }}</view>
        <view class="task-status">
          <text :class="['status-tag', `status-${task.status}`]">{{ task.status }}</text>
        </view>
        <view class="task-progress">
          <text class="progress-text">进度: {{ task.corrected_sheets }}/{{ task.total_sheets }} 份</text>
          <view class="progress-bar">
            <view
              class="progress-inner"
              :style="{width: `${(task.corrected_sheets / task.total_sheets) * 100}%`}"
            ></view>
          </view>
        </view>
        <view class="task-date">创建时间: {{ new Date(task.created_at).toLocaleDateString() }}</view>
      </view>
    </view>

    <view class="empty-state" v-else-if="userStore.isLogin && !correctionStore.loading">
      <text>暂无批改任务数据</text>
    </view>

    <view class="loading" v-if="userStore.isLogin && correctionStore.loading">
      <text>加载中...</text>
    </view>
  </view>
</template>

<style lang="scss">
.page-container {
  padding: 30rpx;

  .welcome-block {
    margin: 60rpx 0;
    text-align: center;

    .title {
      font-size: 40rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
      display: block;
    }

    .desc {
      font-size: 28rpx;
      color: #666;
    }
  }

  .btn-block {
    margin: 60rpx 0;
    display: flex;
    justify-content: center;
  }

  .task-list {
    margin-top: 60rpx;

    .list-header {
      margin-bottom: 20rpx;
      font-size: 30rpx;
      font-weight: bold;
    }

    .task-card {
      margin-bottom: 20rpx;
      padding: 20rpx;

      .task-title {
        font-size: 32rpx;
        font-weight: bold;
        margin-bottom: 16rpx;
      }

      .task-status {
        margin-bottom: 16rpx;

        .status-tag {
          padding: 4rpx 16rpx;
          border-radius: 20rpx;
          font-size: 24rpx;
          color: #fff;

          &.status-待处理 {
            background-color: #ff976a;
          }

          &.status-进行中 {
            background-color: #1989fa;
          }

          &.status-已完成 {
            background-color: #07c160;
          }

          &.status-已失败 {
            background-color: #ee0a24;
          }
        }
      }

      .task-progress {
        margin-bottom: 16rpx;

        .progress-text {
          font-size: 26rpx;
          color: #666;
          margin-bottom: 8rpx;
          display: block;
        }

        .progress-bar {
          height: 10rpx;
          background-color: #ebedf0;
          border-radius: 10rpx;
          overflow: hidden;

          .progress-inner {
            height: 100%;
            background-color: #07c160;
            border-radius: 10rpx;
          }
        }
      }

      .task-date {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .empty-state, .loading {
    margin-top: 60rpx;
    text-align: center;
    color: #999;
    font-size: 28rpx;
  }
}
</style>