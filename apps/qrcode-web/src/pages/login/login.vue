<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useUserStore } from '@/store'

const userStore = useUserStore()
const loading = ref(false)
const errorMessage = ref('')

// 表单数据
const formData = reactive({
  phone: '',
  password: ''
})

// 表单验证
const validateForm = () => {
  if (!formData.phone) {
    uni.showToast({
      title: '请输入手机号',
      icon: 'none'
    })
    return false
  }

  if (!formData.password) {
    uni.showToast({
      title: '请输入密码',
      icon: 'none'
    })
    return false
  }

  return true
}

// 处理登录
const handleLogin = async () => {
  if (!validateForm()) {
    return
  }

  loading.value = true
  errorMessage.value = ''

  try {
    await userStore.login({
      phone: formData.phone,
      password: formData.password
    })

    // 登录成功，跳转到项目选择页面
    uni.reLaunch({
      url: '/pages/project/select'
    })
  } catch (error: any) {
    console.error('登录失败', error)
    errorMessage.value = error.message || '登录失败，请检查账号密码'
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <view class="login-page">
    <view class="login-header">
      <text class="title">叮当全科批改</text>
      <text class="subtitle">请登录您的账号</text>
    </view>

    <view class="login-form">
      <view class="form-item">
        <text class="label">手机号</text>
        <input
          class="input"
          type="number"
          placeholder="请输入手机号"
          v-model="formData.phone"
        />
      </view>

      <view class="form-item">
        <text class="label">密码</text>
        <input
          class="input"
          type="password"
          placeholder="请输入密码"
          v-model="formData.password"
        />
      </view>

      <view class="error-message" v-if="errorMessage">
        <text>{{ errorMessage }}</text>
      </view>

      <button
        class="btn btn-primary login-btn"
        :disabled="loading"
        @click="handleLogin"
      >
        {{ loading ? '登录中...' : '登录' }}
      </button>
    </view>
  </view>
</template>

<style lang="scss">
.login-page {
  padding: 40rpx;
  height: 100vh;
  display: flex;
  flex-direction: column;

  .login-header {
    margin: 80rpx 0;
    text-align: center;

    .title {
      font-size: 40rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
      display: block;
    }

    .subtitle {
      font-size: 28rpx;
      color: #666;
    }
  }

  .login-form {
    margin-top: 60rpx;

    .form-item {
      margin-bottom: 40rpx;

      .label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;
        display: block;
      }

      .input {
        width: 100%;
        height: 80rpx;
        border: 1px solid #ddd;
        border-radius: 8rpx;
        padding: 0 20rpx;
        font-size: 28rpx;
        background-color: #fff;
      }
    }

    .error-message {
      color: #ff4d4f;
      font-size: 24rpx;
      margin-bottom: 30rpx;
    }

    .login-btn {
      margin-top: 60rpx;
      width: 100%;
    }
  }
}
</style>
