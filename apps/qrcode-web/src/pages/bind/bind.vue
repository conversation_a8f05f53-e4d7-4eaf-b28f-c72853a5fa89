<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useProjectStore } from '@/store'
import { bindTask } from '@/api'
import type { CorrectionTask } from '@/api'

const projectStore = useProjectStore()
const loading = ref(false)
const binding = ref(false)
const bindSuccess = ref(false)
const selectedProject = ref<CorrectionTask | null>(null)

// 二维码信息
const qrCodeId = computed(() => projectStore.currentQrCode)

// 绑定项目
const handleBind = async () => {
  if (!selectedProject.value || !qrCodeId.value) {
    uni.showToast({
      title: '请选择要绑定的项目',
      icon: 'none'
    })
    return
  }

  binding.value = true
  try {
    await bindTask(qrCodeId.value, selectedProject.value.id)
    bindSuccess.value = true

    // 3秒后返回首页
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/index/index'
      })
    }, 3000)
  } catch (error) {
    uni.showToast({
      title: '绑定失败，请重试',
      icon: 'none'
    })
    console.error('绑定项目失败', error)
  } finally {
    binding.value = false
  }
}

// 选择项目
const selectProject = (project: CorrectionTask) => {
  selectedProject.value = project
}

// 返回扫描页
const goBackToScan = () => {
  uni.navigateBack()
}

onMounted(() => {
  if (!projectStore.currentQrCode) {
    uni.showToast({
      title: '请先扫描二维码',
      icon: 'none'
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
    return
  }

  // 加载项目数据
  if (projectStore.projects.length === 0) {
    loading.value = true
    projectStore.fetchProjects(0, 10).finally(() => {
      loading.value = false
    })
  }
})
</script>

<template>
  <view class="page-container bind-page">
    <view class="header">
      <text class="title">选择项目绑定</text>
      <text class="qrcode-info">二维码ID: {{ qrCodeId }}</text>
    </view>

    <view class="project-list" v-if="!loading && projectStore.projects.length > 0">
      <view
        class="project-item"
        v-for="project in projectStore.projects"
        :key="project.id"
        :class="{ active: selectedProject && selectedProject.id === project.id }"
        @click="selectProject(project)"
      >
        <view class="project-info">
          <view class="project-name">{{ project.name }}</view>
          <view class="project-desc">{{ project.description }}</view>
        </view>
        <view class="select-icon" v-if="selectedProject && selectedProject.id === project.id">✓</view>
      </view>
    </view>

    <view class="empty-state" v-else-if="!loading">
      <text>暂无可绑定的项目</text>
    </view>

    <view class="loading" v-if="loading">
      <text>加载项目列表中...</text>
    </view>

    <view class="bind-success" v-if="bindSuccess">
      <view class="success-icon">✓</view>
      <text class="success-message">项目绑定成功</text>
      <text class="success-hint">即将返回首页...</text>
    </view>

    <view class="action-buttons" v-if="!bindSuccess">
      <button
        class="btn btn-primary"
        @click="handleBind"
        :disabled="binding || !selectedProject"
      >
        {{ binding ? '绑定中...' : '确认绑定' }}
      </button>
      <button class="btn" @click="goBackToScan">返回扫描</button>
    </view>
  </view>
</template>

<style lang="scss">
.bind-page {
  padding: 30rpx;

  .header {
    margin: 40rpx 0;
    text-align: center;

    .title {
      font-size: 36rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
      display: block;
    }

    .qrcode-info {
      font-size: 24rpx;
      color: #666;
      background-color: #f0f0f0;
      padding: 10rpx 20rpx;
      border-radius: 20rpx;
    }
  }

  .project-list {
    margin: 40rpx 0;

    .project-item {
      background-color: #fff;
      border-radius: 8rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

      &.active {
        border: 2rpx solid #3cc51f;
        background-color: rgba(60, 197, 31, 0.05);
      }

      .project-info {
        flex: 1;

        .project-name {
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 10rpx;
        }

        .project-desc {
          font-size: 28rpx;
          color: #666;
        }
      }

      .select-icon {
        width: 60rpx;
        height: 60rpx;
        background-color: #3cc51f;
        border-radius: 50%;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
      }
    }
  }

  .empty-state, .loading {
    margin: 100rpx 0;
    text-align: center;
    color: #999;
    font-size: 28rpx;
  }

  .bind-success {
    margin: 100rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;

    .success-icon {
      width: 120rpx;
      height: 120rpx;
      background-color: #3cc51f;
      border-radius: 50%;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 60rpx;
      margin-bottom: 30rpx;
    }

    .success-message {
      font-size: 36rpx;
      font-weight: bold;
      color: #3cc51f;
      margin-bottom: 20rpx;
    }

    .success-hint {
      font-size: 24rpx;
      color: #999;
    }
  }

  .action-buttons {
    position: fixed;
    bottom: 60rpx;
    left: 0;
    right: 0;
    padding: 0 30rpx;
    display: flex;
    flex-direction: column;

    .btn {
      margin-bottom: 20rpx;
    }
  }
}
</style>