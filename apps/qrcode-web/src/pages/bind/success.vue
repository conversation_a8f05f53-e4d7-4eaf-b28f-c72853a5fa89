<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useCorrectionStore } from '@/store'

const correctionStore = useCorrectionStore()
const countdown = ref(5)

// 获取过期时间的格式化字符串
const expireTimeFormatted = computed(() => {
  if (!correctionStore.bindExpireAt) return ''
  const date = new Date(correctionStore.bindExpireAt * 1000)
  return `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`
})

// 获取任务信息
const task = computed(() => correctionStore.currentTask)

// 返回首页
const goToHome = () => {
  uni.reLaunch({
    url: '/pages/index/index'
  })
}

onMounted(() => {
  // 如果没有绑定信息，直接返回首页
  if (!correctionStore.bindToken || !task.value) {
    uni.showToast({
      title: '缺少绑定信息',
      icon: 'none'
    })
    setTimeout(() => {
      goToHome()
    }, 1500)
    return
  }

  // 5秒倒计时后自动返回首页
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
      goToHome()
    }
  }, 1000)
})
</script>

<template>
  <view class="page-container success-page">
    <view class="success-icon">
      <text class="icon">✓</text>
    </view>

    <view class="success-title">
      <text>绑定成功</text>
    </view>

    <view class="info-card" v-if="task">
      <view class="info-header">任务信息</view>
      <view class="info-item">
        <text class="label">任务标题：</text>
        <text class="value">{{ task.title }}</text>
      </view>
      <view class="info-item">
        <text class="label">任务状态：</text>
        <text class="value">{{ task.status }}</text>
      </view>
      <view class="info-item">
        <text class="label">完成情况：</text>
        <text class="value">{{ task.corrected_sheets }}/{{ task.total_sheets }}</text>
      </view>
      <view class="info-item">
        <text class="label">绑定有效期至：</text>
        <text class="value">{{ expireTimeFormatted }}</text>
      </view>
    </view>

    <view class="token-info">
      <text class="token-label">访问令牌：</text>
      <text class="token-value">{{ correctionStore.bindToken }}</text>
    </view>

    <view class="countdown">
      <text>{{ countdown }}秒后自动返回首页</text>
    </view>

    <view class="action-buttons">
      <button class="btn btn-primary" @click="goToHome">返回首页</button>
    </view>
  </view>
</template>

<style lang="scss">
.success-page {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  .success-icon {
    margin-top: 60rpx;
    width: 160rpx;
    height: 160rpx;
    background-color: #07c160;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .icon {
      color: #fff;
      font-size: 80rpx;
    }
  }

  .success-title {
    margin: 40rpx 0;
    font-size: 40rpx;
    font-weight: bold;
    color: #07c160;
  }

  .info-card {
    width: 100%;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .info-header {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
      padding-bottom: 20rpx;
      border-bottom: 1rpx solid #eee;
    }

    .info-item {
      display: flex;
      margin-bottom: 16rpx;

      .label {
        width: 200rpx;
        color: #666;
        font-size: 28rpx;
      }

      .value {
        flex: 1;
        font-size: 28rpx;
        font-weight: 500;
      }
    }
  }

  .token-info {
    width: 100%;
    margin-bottom: 30rpx;
    background-color: #f8f8f8;
    padding: 20rpx;
    border-radius: 8rpx;

    .token-label {
      font-size: 28rpx;
      color: #666;
      display: block;
      margin-bottom: 10rpx;
    }

    .token-value {
      font-size: 26rpx;
      color: #333;
      word-break: break-all;
    }
  }

  .countdown {
    margin: 40rpx 0;
    font-size: 28rpx;
    color: #999;
  }

  .action-buttons {
    width: 100%;
    margin-top: 30rpx;

    .btn {
      width: 100%;
    }
  }
}
</style>