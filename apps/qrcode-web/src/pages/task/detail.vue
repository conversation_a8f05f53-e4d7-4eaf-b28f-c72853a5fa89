<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useCorrectionStore } from '@/store'
import type { CorrectionTask } from '@/api'

const correctionStore = useCorrectionStore()
const loading = ref(false)
const task = ref<CorrectionTask | null>(null)

// 获取任务ID
const getTaskId = () => {
  const pages = getCurrentPages()
  const page = pages[pages.length - 1]
  // @ts-ignore
  const options = page?.options || {}
  return options.id || ''
}

// 查找任务详情
const findTask = (id: string) => {
  return correctionStore.tasks.find(task => task.id === id) || null
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'waiting': '待处理',
    'processing': '进行中',
    'completed': '已完成',
    'failed': '已失败'
  }
  return statusMap[status] || status
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'waiting': '#ff976a',
    'processing': '#1989fa',
    'completed': '#07c160',
    'failed': '#ee0a24'
  }
  return colorMap[status] || '#999'
}

// 扫描二维码和绑定
const scanAndBind = () => {
  if (!task.value) return

  // 设置当前选中的任务
  correctionStore.setCurrentTask(task.value)

  // 直接跳转到扫描页面，并传递任务ID
  uni.navigateTo({
    url: `/pages/scan/scan?taskId=${task.value.id}&fromDetail=true`
  })
}

// 加载任务详情
onMounted(async () => {
  const taskId = getTaskId()
  if (!taskId) {
    uni.showToast({
      title: '缺少任务ID',
      icon: 'none'
    })
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
    return
  }

  // 如果已有任务列表中存在该任务，直接使用
  const existingTask = findTask(taskId)
  if (existingTask) {
    task.value = existingTask
    return
  }

  // 否则加载任务列表
  loading.value = true
  try {
    await correctionStore.fetchTasks(0, 10)
    const foundTask = findTask(taskId)
    if (foundTask) {
      task.value = foundTask
    } else {
      throw new Error('任务不存在')
    }
  } catch (error) {
    uni.showToast({
      title: '获取任务详情失败',
      icon: 'none'
    })
    console.error('获取任务详情失败', error)
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } finally {
    loading.value = false
  }
})
</script>

<template>
  <view class="page-container detail-page">
    <view class="loading" v-if="loading">
      <text>加载中...</text>
    </view>

    <block v-if="task && !loading">
      <view class="detail-header">
        <view class="task-title">{{ task.title }}</view>
        <view class="task-status">
          <text class="status-tag" :style="{backgroundColor: getStatusColor(task.status)}">
            {{ getStatusText(task.status) }}
          </text>
        </view>
      </view>

      <view class="detail-card">
        <view class="card-header">基本信息</view>
        <view class="card-content">
          <view class="info-item">
            <text class="label">任务ID：</text>
            <text class="value">{{ task.id }}</text>
          </view>
          <view class="info-item">
            <text class="label">创建时间：</text>
            <text class="value">{{ new Date(task.createdAt).toLocaleString() }}</text>
          </view>
          <view class="info-item">
            <text class="label">更新时间：</text>
            <text class="value">{{ new Date(task.updatedAt).toLocaleString() }}</text>
          </view>
        </view>
      </view>

      <view class="detail-card">
        <view class="card-header">批改配置</view>
        <view class="card-content">
          <view class="info-item">
            <text class="label">是否需要扫描试卷题目：</text>
            <text class="value">{{ task.need_scan_paper ? '是' : '否' }}</text>
          </view>
          <view class="info-item">
            <text class="label">答题卡和试卷是否在一起：</text>
            <text class="value">{{ task.paper_with_answer ? '是' : '否' }}</text>
          </view>
          <view class="info-item">
            <text class="label">是否自动批改：</text>
            <text class="value">{{ task.auto_correction ? '是' : '否' }}</text>
          </view>
          <view class="info-item">
            <text class="label">每张答题卡纸张数：</text>
            <text class="value">{{ task.sheets_per_paper }}</text>
          </view>
          <view class="info-item">
            <text class="label">是否双面打印：</text>
            <text class="value">{{ task.double_sided ? '是' : '否' }}</text>
          </view>
        </view>
      </view>

      <view class="detail-card">
        <view class="card-header">批改进度</view>
        <view class="card-content">
          <view class="info-item">
            <text class="label">总答题卡数量：</text>
            <text class="value">{{ task.total_sheets }}</text>
          </view>
          <view class="info-item">
            <text class="label">已扫描答题卡数量：</text>
            <text class="value">{{ task.scanned_sheets }}</text>
          </view>
          <view class="info-item">
            <text class="label">已批改答题卡数量：</text>
            <text class="value">{{ task.corrected_sheets }}</text>
          </view>
          <view class="progress-bar">
            <view
              class="progress-inner"
              :style="{
                width: `${task.total_sheets ? (task.corrected_sheets / task.total_sheets) * 100 : 0}%`,
                backgroundColor: getStatusColor(task.status)
              }"
            ></view>
          </view>
          <view class="progress-text">
            <text>{{ Math.floor(task.total_sheets ? (task.corrected_sheets / task.total_sheets) * 100 : 0) }}%</text>
          </view>
        </view>
      </view>

      <view class="action-buttons">
        <button class="btn btn-primary" @click="scanAndBind">
          扫描二维码并绑定此任务
        </button>
        <button class="btn" @click="uni.navigateBack()">返回</button>
      </view>
    </block>
  </view>
</template>

<style lang="scss">
.detail-page {
  padding: 30rpx;

  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300rpx;
    color: #999;
    font-size: 28rpx;
  }

  .detail-header {
    margin-bottom: 30rpx;

    .task-title {
      font-size: 36rpx;
      font-weight: bold;
      margin-bottom: 16rpx;
    }

    .task-status {
      .status-tag {
        padding: 6rpx 20rpx;
        border-radius: 20rpx;
        font-size: 24rpx;
        color: #fff;
      }
    }
  }

  .detail-card {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

    .card-header {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
      padding-bottom: 16rpx;
      border-bottom: 1rpx solid #eee;
    }

    .card-content {
      .info-item {
        display: flex;
        margin-bottom: 16rpx;
        flex-wrap: wrap;

        .label {
          width: 280rpx;
          color: #666;
          font-size: 28rpx;
        }

        .value {
          flex: 1;
          font-size: 28rpx;
          color: #333;
        }
      }

      .progress-bar {
        height: 16rpx;
        background-color: #ebedf0;
        border-radius: 16rpx;
        overflow: hidden;
        margin: 20rpx 0 10rpx 0;

        .progress-inner {
          height: 100%;
          border-radius: 16rpx;
        }
      }

      .progress-text {
        text-align: right;
        font-size: 24rpx;
        color: #666;
      }
    }
  }

  .action-buttons {
    margin-top: 40rpx;
    padding-bottom: 40rpx;

    .btn {
      margin-bottom: 20rpx;
      width: 100%;
    }
  }
}
</style>