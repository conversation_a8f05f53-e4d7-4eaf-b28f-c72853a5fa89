<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useProjectStore, useCorrectionStore, useUserStore } from '@/store'
import { bindTask } from '@/api'
import type { CorrectionTask } from '@/api'
import { getSelectedProject } from '@/utils/auth'

const projectStore = useProjectStore()
const correctionStore = useCorrectionStore()
const userStore = useUserStore()
const loading = ref(false)
const binding = ref(false)
const bindSuccess = ref(false)
const selectedTask = ref<CorrectionTask | null>(null)

// 二维码信息
const qrCodeId = computed(() => projectStore.currentQrCode)

// 加载任务列表
onMounted(async () => {
  // 检查登录状态
  if (!userStore.checkLoginStatus()) {
    uni.redirectTo({
      url: '/pages/login/login'
    })
    return
  }

  // 检查是否已选择项目
  const selectedProject = getSelectedProject()
  if (!selectedProject) {
    uni.redirectTo({
      url: '/pages/project/select'
    })
    return
  }

  if (!qrCodeId.value) {
    uni.showToast({
      title: '请先扫描二维码',
      icon: 'none'
    })
    setTimeout(() => {
      uni.redirectTo({
        url: '/pages/scan/scan'
      })
    }, 1500)
    return
  }

  // 加载任务数据
  loading.value = true
  try {
    await correctionStore.fetchTasks(0, 10)
  } catch (error) {
    console.error('获取批改任务失败', error)
  } finally {
    loading.value = false
  }
})

// 绑定任务
const handleBind = async () => {
  if (!selectedTask.value || !qrCodeId.value) {
    uni.showToast({
      title: '请选择要绑定的任务',
      icon: 'none'
    })
    return
  }

  binding.value = true
  try {
    const response = await bindTask(qrCodeId.value, selectedTask.value.id)

    // 保存绑定结果
    correctionStore.setBindResult(response.token, response.expireAt)
    correctionStore.setCurrentTask(selectedTask.value)
    bindSuccess.value = true

    // 3秒后返回首页
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/bind/success'
      })
    }, 1500)
  } catch (error) {
    uni.showToast({
      title: '绑定失败，请重试',
      icon: 'none'
    })
    console.error('绑定任务失败', error)
  } finally {
    binding.value = false
  }
}

// 选择任务
const selectTask = (task: CorrectionTask) => {
  selectedTask.value = task
}

// 返回扫描页
const goBackToScan = () => {
  uni.navigateBack()
}

// 获取任务状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'waiting': '待处理',
    'processing': '进行中',
    'completed': '已完成',
    'failed': '已失败'
  }
  return statusMap[status] || status
}

// 获取任务状态颜色
const getStatusColor = (status: string) => {
  const colorMap: Record<string, string> = {
    'waiting': '#ff976a',
    'processing': '#1989fa',
    'completed': '#07c160',
    'failed': '#ee0a24'
  }
  return colorMap[status] || '#999'
}
</script>

<template>
  <view class="page-container task-select-page">
    <view class="header">
      <text class="title">选择批改任务绑定</text>
      <text class="qrcode-info">二维码ID: {{ qrCodeId }}</text>
    </view>

    <view class="task-list" v-if="!loading && correctionStore.tasks.length > 0">
      <view
        class="task-item"
        v-for="task in correctionStore.tasks"
        :key="task.id"
        :class="{ active: selectedTask && selectedTask.id === task.id }"
        @click="selectTask(task)"
      >
        <view class="task-info">
          <view class="task-title">{{ task.title }}</view>
          <view class="task-status">
            <text class="status-tag" :style="{backgroundColor: getStatusColor(task.status)}">
              {{ getStatusText(task.status) }}
            </text>
          </view>
          <view class="task-progress">
            <text class="progress-text">进度: {{ task.corrected_sheets }}/{{ task.total_sheets }}</text>
            <view class="progress-bar">
              <view
                class="progress-inner"
                :style="{
                  width: `${task.total_sheets ? (task.corrected_sheets / task.total_sheets) * 100 : 0}%`,
                  backgroundColor: getStatusColor(task.status)
                }"
              ></view>
            </view>
          </view>
        </view>
        <view class="select-icon" v-if="selectedTask && selectedTask.id === task.id">✓</view>
      </view>
    </view>

    <view class="empty-state" v-else-if="!loading">
      <text>暂无可绑定的批改任务</text>
    </view>

    <view class="loading" v-if="loading">
      <text>加载任务列表中...</text>
    </view>

    <view class="bind-success" v-if="bindSuccess">
      <view class="success-icon">✓</view>
      <text class="success-message">任务绑定成功</text>
      <text class="success-hint">即将跳转...</text>
    </view>

    <view class="action-buttons" v-if="!bindSuccess">
      <button
        class="btn btn-primary"
        @click="handleBind"
        :disabled="binding || !selectedTask"
      >
        {{ binding ? '绑定中...' : '确认绑定' }}
      </button>
      <button class="btn" @click="goBackToScan">返回扫描</button>
    </view>
  </view>
</template>

<style lang="scss">
.task-select-page {
  padding: 30rpx;

  .header {
    margin: 40rpx 0;
    text-align: center;

    .title {
      font-size: 36rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
      display: block;
    }

    .qrcode-info {
      font-size: 24rpx;
      color: #666;
      background-color: #f0f0f0;
      padding: 10rpx 20rpx;
      border-radius: 20rpx;
    }
  }

  .task-list {
    margin: 40rpx 0;

    .task-item {
      background-color: #fff;
      border-radius: 8rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

      &.active {
        border: 2rpx solid #3cc51f;
        background-color: rgba(60, 197, 31, 0.05);
      }

      .task-info {
        flex: 1;

        .task-title {
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 16rpx;
        }

        .task-status {
          margin-bottom: 16rpx;

          .status-tag {
            padding: 4rpx 16rpx;
            border-radius: 20rpx;
            font-size: 24rpx;
            color: #fff;
          }
        }

        .task-progress {
          .progress-text {
            font-size: 26rpx;
            color: #666;
            margin-bottom: 8rpx;
          }

          .progress-bar {
            height: 10rpx;
            background-color: #ebedf0;
            border-radius: 10rpx;
            overflow: hidden;

            .progress-inner {
              height: 100%;
              border-radius: 10rpx;
            }
          }
        }
      }

      .select-icon {
        width: 60rpx;
        height: 60rpx;
        background-color: #3cc51f;
        border-radius: 50%;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
      }
    }
  }

  .empty-state, .loading {
    margin: 100rpx 0;
    text-align: center;
    color: #999;
    font-size: 28rpx;
  }

  .bind-success {
    margin: 100rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;

    .success-icon {
      width: 120rpx;
      height: 120rpx;
      background-color: #3cc51f;
      border-radius: 50%;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 60rpx;
      margin-bottom: 30rpx;
    }

    .success-message {
      font-size: 36rpx;
      font-weight: bold;
      color: #3cc51f;
      margin-bottom: 20rpx;
    }

    .success-hint {
      font-size: 24rpx;
      color: #999;
    }
  }

  .action-buttons {
    position: fixed;
    bottom: 60rpx;
    left: 0;
    right: 0;
    padding: 0 30rpx;
    display: flex;
    flex-direction: column;

    .btn {
      margin-bottom: 20rpx;
    }
  }
}
</style>