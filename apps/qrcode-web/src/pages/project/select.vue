<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useProjectStore, useUserStore } from '@/store'
import { setSelectedProject } from '@/utils/auth'
import { bindTask, scanPrinter } from '@/api'
import type { CorrectionTask } from '@/api'

const projectStore = useProjectStore()
const userStore = useUserStore()
const loading = ref(false)
const binding = ref(false)
const bindSuccess = ref(false)
const selectedProject = ref<CorrectionTask | null>(null)
const qrCodeId = ref('')
const errorMessage = ref('')

// 获取URL参数
const getPageParams = () => {
  try {
    // 先尝试获取当前页面参数
    const pages = getCurrentPages()
    const page = pages[pages.length - 1]
    // @ts-ignore
    if (page?.options) {
      return page.options
    }

    // 如果在H5环境下，使用URL参数
    // @ts-ignore
    if (window && window.location) {
      const url = new URL(window.location.href)
      const params = {}
      url.searchParams.forEach((value, key) => {
        params[key] = value
      })
      return params
    }
  } catch (error) {
    console.error('获取URL参数失败', error)
  }

  // 如果上述方法都失败，返回空对象
  return {}
}

// 验证二维码
const validateQrCode = async (qrCodeId: string) => {
  try {
    // 调用scan接口验证二维码
    const response = await scanPrinter(qrCodeId)
    if (response.success) {
      return true
    } else {
      uni.showToast({
        title: response.message || '无效的二维码',
        icon: 'none'
      })
      return false
    }
  } catch (error) {
    console.error('验证二维码失败', error)
    uni.showToast({
      title: '验证二维码失败',
      icon: 'none'
    })
    return false
  }
}

// 加载项目列表
onMounted(async () => {
  // 检查登录状态
  if (!userStore.checkLoginStatus()) {
    uni.redirectTo({
      url: '/pages/login/login'
    })
    return
  }

  // 获取URL参数中的qr_code_id
  const params = getPageParams()
  const urlQrCodeId = params.qr_code_id || ''

  // 如果有qr_code_id，先验证二维码
  if (urlQrCodeId) {
    const isValid = await validateQrCode(urlQrCodeId)
    if (isValid) {
      qrCodeId.value = urlQrCodeId
      projectStore.setCurrentQrCode(urlQrCodeId)
    }
  }

  loading.value = true
  try {
    await projectStore.fetchProjects(0, 10)
  } catch (error) {
    console.error('获取项目列表失败', error)
  } finally {
    loading.value = false
  }
})

// 选择项目
const selectProject = (project: CorrectionTask) => {
  selectedProject.value = project
  projectStore.setCurrentProject(project)
}

// 绑定项目
const bindProject = async () => {
  if (!selectedProject.value) {
    uni.showToast({
      title: '请先选择批改任务',
      icon: 'none'
    })
    return
  }

  // 如果没有qr_code_id，则跳转到扫描页面
  if (!qrCodeId.value) {
    // 保存选中的批改任务
    setSelectedProject(selectedProject.value)

    // 跳转到扫描页面
    uni.navigateTo({
      url: '/pages/scan/scan'
    })
    return
  }

  // 如果有qr_code_id，则直接绑定
  binding.value = true
  errorMessage.value = ''

  try {
    const response = await bindTask(qrCodeId.value, selectedProject.value.id)

    // 保存绑定结果
    projectStore.setBindResult(response.token, response.expireAt)
    bindSuccess.value = true

    // 显示成功提示
    uni.showToast({
      title: '绑定成功',
      icon: 'success'
    })

    // 跳转到首页
    setTimeout(() => {
      uni.reLaunch({
        url: '/pages/index/index'
      })
    }, 1500)
  } catch (error) {
    errorMessage.value = '绑定失败，请重试'
    uni.showToast({
      title: '绑定失败，请重试',
      icon: 'none'
    })
    console.error('绑定项目失败', error)
  } finally {
    binding.value = false
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'waiting_for_settings': '等待设置',
    'waiting_for_paper': '等待试卷',
    'waiting_for_answer': '等待答案',
    'scanning': '扫描中',
    'completed': '已完成'
  }
  return statusMap[status] || status
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 退出登录
const handleLogout = async () => {
  await userStore.logout()
  uni.reLaunch({
    url: '/pages/login/login'
  })
}
</script>

<template>
  <view class="task-select-page">
    <view class="header">
      <text class="title">选择批改任务</text>
      <text class="subtitle">请选择要操作的批改任务</text>
      <text class="qrcode-info" v-if="qrCodeId">二维码ID: {{ qrCodeId }}</text>
    </view>

    <view class="task-list" v-if="!loading && projectStore.projects.length > 0">
      <view
        class="task-item"
        v-for="project in projectStore.projects"
        :key="project.id"
        :class="{ active: selectedProject && selectedProject.id === project.id }"
        @click="selectProject(project)"
      >
        <view class="task-info">
          <view class="task-name">{{ project.title }}</view>
          <view class="task-desc">状态: {{ getStatusText(project.status) }}</view>
          <view class="task-progress">
            <text class="progress-text">进度: {{ project.corrected_sheets }}/{{ project.total_sheets }} 份</text>
            <view class="progress-bar">
              <view
                class="progress-inner"
                :style="{width: `${project.total_sheets ? (project.corrected_sheets / project.total_sheets) * 100 : 0}%`}"
              ></view>
            </view>
          </view>
          <view class="task-date">创建时间: {{ new Date(project.created_at).toLocaleDateString() }}</view>
        </view>
        <view class="select-icon" v-if="selectedProject && selectedProject.id === project.id">✓</view>
      </view>
    </view>

    <view class="empty-state" v-else-if="!loading">
      <text>暂无可选择的批改任务</text>
    </view>

    <view class="loading" v-if="loading">
      <text>加载批改任务列表中...</text>
    </view>

    <view class="action-buttons">
      <button
        class="btn btn-primary"
        @click="bindProject"
        :disabled="!selectedProject || binding"
      >
        {{ qrCodeId ? '绑定项目' : '扫描二维码' }}
      </button>
      <button class="btn" @click="goBack">返回</button>
      <button class="btn" @click="handleLogout">退出登录</button>
    </view>
  </view>
</template>

<style lang="scss">
.task-select-page {
  padding: 30rpx;

  .header {
    margin: 40rpx 0;
    text-align: center;

    .title {
      font-size: 36rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
      display: block;
    }

    .subtitle {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 16rpx;
    }

    .qrcode-info {
      display: inline-block;
      font-size: 24rpx;
      color: #666;
      background-color: #f0f0f0;
      padding: 8rpx 20rpx;
      border-radius: 20rpx;
    }
  }

  .task-list {
    margin: 40rpx 0;

    .task-item {
      background-color: #fff;
      border-radius: 8rpx;
      padding: 30rpx;
      margin-bottom: 20rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

      &.active {
        border: 2rpx solid #3cc51f;
        background-color: rgba(60, 197, 31, 0.05);
      }

      .task-info {
        flex: 1;

        .task-name {
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 16rpx;
        }

        .task-desc {
          font-size: 28rpx;
          color: #666;
          margin-bottom: 16rpx;
        }

        .task-progress {
          margin-bottom: 16rpx;

          .progress-text {
            font-size: 26rpx;
            color: #666;
            margin-bottom: 8rpx;
            display: block;
          }

          .progress-bar {
            height: 10rpx;
            background-color: #ebedf0;
            border-radius: 10rpx;
            overflow: hidden;

            .progress-inner {
              height: 100%;
              background-color: #07c160;
              border-radius: 10rpx;
            }
          }
        }

        .task-date {
          font-size: 24rpx;
          color: #999;
        }
      }

      .select-icon {
        width: 60rpx;
        height: 60rpx;
        background-color: #3cc51f;
        border-radius: 50%;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32rpx;
      }
    }
  }

  .empty-state, .loading {
    margin: 100rpx 0;
    text-align: center;
    color: #999;
    font-size: 28rpx;
  }

  .action-buttons {
    position: fixed;
    bottom: 60rpx;
    left: 0;
    right: 0;
    padding: 0 30rpx;
    display: flex;
    flex-direction: column;

    .btn {
      margin-bottom: 20rpx;
    }
  }
}
</style>
