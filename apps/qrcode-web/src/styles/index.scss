/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica,
    Segoe UI, Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei',
    sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  background-color: #f8f8f8;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 公共样式 */
.page-container {
  padding: 20rpx;
  box-sizing: border-box;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

/* 按钮样式 */
.btn {
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 30rpx;
  padding: 0 30rpx;
  text-align: center;
}

.btn-primary {
  background-color: #3cc51f;
  color: #fff;
}

/* 表单样式 */
.form-item {
  margin-bottom: 20rpx;
}

.form-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.form-input {
  height: 80rpx;
  background-color: #fff;
  border-radius: 4rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
} 