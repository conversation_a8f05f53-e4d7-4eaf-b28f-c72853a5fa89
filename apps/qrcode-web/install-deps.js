const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 检查package.json是否存在
const packageJsonPath = path.join(__dirname, 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('package.json not found!');
  process.exit(1);
}

// 读取package.json
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

// 检查是否已安装html5-qrcode
if (!packageJson.dependencies['html5-qrcode']) {
  console.log('Installing html5-qrcode...');
  try {
    execSync('npm install html5-qrcode --save', { stdio: 'inherit' });
    console.log('html5-qrcode installed successfully!');
  } catch (error) {
    console.error('Failed to install html5-qrcode:', error);
    process.exit(1);
  }
} else {
  console.log('html5-qrcode is already installed.');
}

console.log('All dependencies are installed.');
