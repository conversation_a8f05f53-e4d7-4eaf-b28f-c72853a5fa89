<template>
  <div class="container">
    <div class="content">
      <div class="title">加入租户</div>
      <div class="form">
        <a-form ref="form" :model="formData" class="form" layout="vertical" @submit="handleSubmit">
          <a-form-item
            field="tenantCode"
            :rules="[{ required: true, message: '租户编码不能为空' }]"
            :validate-trigger="['change', 'blur']"
            hide-label
          >
            <a-input v-model="formData.tenantCode" placeholder="请输入租户编码"> </a-input>
          </a-form-item>
          <a-space :size="16" direction="vertical">
            <a-button type="primary" html-type="submit" long :loading="loading">
              申请加入
            </a-button>
          </a-space>
        </a-form>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import useLoading from '@/hooks/loading'
import { Message, ValidatedError } from '@arco-design/web-vue'
import { ref } from 'vue'
import { joinTenant } from '@/api/tenant'

const { loading, setLoading } = useLoading()

const formData = ref({
  tenantCode: '',
})

const handleSubmit = async ({
  errors,
}: {
  errors: Record<string, ValidatedError> | undefined
}): Promise<void> => {
  if (loading.value) return
  if (errors !== undefined) {
    return
  }
  setLoading(true)
  try {
    joinTenant({
      tenantCode: formData.value.tenantCode,
    }).then(() => {
      Message.success('申请成功')
    })
  } catch (err) {
    console.error(err)
  } finally {
    setLoading(false)
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f5f5f5;
}

.title {
  color: var(--color-text-1);
  font-weight: 500;
  font-size: 24px;
  line-height: 32px;
  margin-bottom: 12px;
}

.sub-title {
  color: var(--color-text-3);
  font-size: 16px;
  line-height: 24px;
}

.form {
  width: 320px;
}
</style>
