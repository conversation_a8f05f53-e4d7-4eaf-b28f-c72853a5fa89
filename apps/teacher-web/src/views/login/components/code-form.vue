<template>
  <a-form
    ref="loginForm"
    :model="userInfo"
    class="login-form"
    layout="vertical"
    @submit="handleSubmit"
  >
    <a-form-item
      field="phone"
      :rules="[{ required: true, message: '用户名不能为空' }]"
      :validate-trigger="['change', 'blur']"
      hide-label
    >
      <a-input v-model="userInfo.phone" placeholder="请输入手机号">
        <template #prefix>
          <icon-user />
        </template>
      </a-input>
    </a-form-item>
    <a-form-item
      field="password"
      :rules="[{ required: true, message: '验证码不能为空' }]"
      :validate-trigger="['change', 'blur']"
      hide-label
    >
      <a-input v-model="userInfo.password" placeholder="请输入验证码" allow-clear>
        <template #suffix>
          <a-button
            type="text"
            size="small"
            class="code-btn"
            :loading="smsLoading"
            :disabled="countdown > 0"
            @click="handleGetCode"
          >
            {{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}
          </a-button>
        </template>
      </a-input>
    </a-form-item>
    <a-space :size="16" direction="vertical">
      <!-- <div class="password-actions">
          <a-link>忘记密码</a-link>
        </div> -->
      <a-button type="primary" html-type="submit" long :loading="loading"> 登录/注册 </a-button>
      <!-- <a-button
          type="text"
          long
          class="register-btn"
          @click="handleRegister"
        >
          注册
        </a-button> -->
    </a-space>
  </a-form>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { type ValidatedError } from '@arco-design/web-vue/es/form/interface'
import { useUserStore } from '@/store'
import useLoading from '@/hooks/loading'
import { sendSmsCode } from '@/api/auth'

const router = useRouter()
const { loading, setLoading } = useLoading()
const { loading: smsLoading, setLoading: setSmsLoading } = useLoading()
const userStore = useUserStore()
const countdown = ref(0)

const userInfo = reactive({
  phone: '',
  password: '',
})

const startCountdown = (): void => {
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

const handleGetCode = async (): Promise<void> => {
  if (loading.value || countdown.value > 0) return
  setSmsLoading(true)
  try {
    if (userInfo.phone === '') {
      Message.error('请输入手机号')
      return
    }
    if (!/^1[3-9]\d{9}$/.test(userInfo.phone)) {
      Message.error('请输入正确的手机号')
      return
    }
    await sendSmsCode(userInfo.phone)
    Message.success('验证码已发送')
    startCountdown()
  } catch (err) {
    console.error(err)
  } finally {
    setSmsLoading(false)
  }
}

const handleSubmit = async ({
  errors,
  values,
}: {
  errors: Record<string, ValidatedError> | undefined
  values: Record<string, any>
}): Promise<void> => {
  if (loading.value) return
  if (errors !== undefined) {
    return
  }
  setLoading(true)
  try {
    await userStore.loginByCode(values.phone, values.password)
    const { redirect, ...othersQuery } = router.currentRoute.value.query
    void router.push({
      name: (redirect as string) ?? 'Workplace',
      query: {
        ...othersQuery,
      },
    })
    Message.success('欢迎使用')
  } catch (err) {
    console.error(err)
  } finally {
    setLoading(false)
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  width: 320px;

  .title {
    color: var(--color-text-1);
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
  }

  .sub-title {
    color: var(--color-text-3);
    font-size: 16px;
    line-height: 24px;
  }

  .error-msg {
    height: 32px;
    color: rgb(var(--red-6));
    line-height: 32px;
  }

  .password-actions {
    display: flex;
    justify-content: space-between;
  }

  .register-btn {
    color: var(--color-text-3) !important;
  }
}
</style>
