<template>
  <div class="container">
    <a-card class="general-card" :title="$route.meta.locale">
      <template #extra>
        <a-button type="primary" @click="goBack">
          <template #icon><icon-arrow-left /></template>
          返回任务详情
        </a-button>
      </template>

      <a-spin style="display: block" :loading="loading">
        <!-- 题目正确率统计 -->
        <div class="section">
          <h3 class="section-title">题目正确率统计</h3>
          <a-table
            :columns="accuracyColumns"
            :data="accuracyStats"
            :pagination="false"
            :loading="accuracyLoading"
            row-key="main_question"
          >
            <template #sub_questions="{ record }">
              <div v-if="record.sub_questions && record.sub_questions.length > 0">
                <a-tag
                  v-for="sub in record.sub_questions"
                  :key="sub.sub_question"
                  size="small"
                  class="mb-1 mr-1"
                >
                  {{ sub.sub_question }}: {{ (sub.accuracy * 100).toFixed(1) }}%
                </a-tag>
              </div>
              <span v-else>-</span>
            </template>
          </a-table>
        </div>

        <!-- 题目批改结果 -->
        <div class="section">
          <div class="section-header">
            <h3 class="section-title">题目批改结果</h3>
            <a-space>
              <a-select
                v-model="selectedMainQuestion"
                placeholder="选择大题"
                style="width: 200px"
                @change="handleMainQuestionChange"
              >
                <a-option
                  v-for="stat in accuracyStats"
                  :key="stat.main_question"
                  :value="stat.main_question"
                >
                  {{ stat.main_question }}
                </a-option>
              </a-select>
              <a-switch
                v-model="includeCorrect"
                @change="handleIncludeCorrectChange"
              >
                <template #checked>包含正确答案</template>
                <template #unchecked>仅错题</template>
              </a-switch>
            </a-space>
          </div>

          <a-table
            :columns="resultsColumns"
            :data="questionResults"
            :pagination="resultsPagination"
            :loading="resultsLoading"
            row-key="id"
            @page-change="handlePageChange"
          >
            <template #score="{ record }">
              <span v-if="record.score >= 0">{{ record.score }}</span>
              <span v-else class="text-gray">无评分标准</span>
            </template>
            <template #right="{ record }">
              <a-progress
                :percent="Math.round(record.right * 100)"
                :width="80"
                size="small"
                :show-text="false"
              />
              <span class="ml-2">{{ (record.right * 100).toFixed(1) }}%</span>
            </template>
          </a-table>
        </div>
      </a-spin>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { IconArrowLeft } from '@arco-design/web-vue/es/icon'
import {
  getQuestionAccuracyStats,
  getQuestionResults,
  type QuestionAccuracyStats,
  type QuestionResultDetailDTO,
  type QuestionResultsRequest,
} from '@/api/correction'

const route = useRoute()
const router = useRouter()
const taskId = computed(() => route.params.taskId as string)

const loading = ref(false)
const accuracyLoading = ref(false)
const resultsLoading = ref(false)

// 题目正确率统计数据
const accuracyStats = ref<QuestionAccuracyStats[]>([])

// 题目批改结果数据
const questionResults = ref<QuestionResultDetailDTO[]>([])
const selectedMainQuestion = ref<string>('')
const includeCorrect = ref(false)

// 分页配置
const resultsPagination = ref({
  current: 1,
  pageSize: 20,
  total: 0,
  showTotal: true,
  showPageSize: true,
})

// 题目正确率统计表格列配置
const accuracyColumns = [
  {
    title: '大题题号',
    dataIndex: 'main_question',
    width: 120,
  },
  {
    title: '平均正确率',
    dataIndex: 'average_accuracy',
    width: 120,
    render: ({ record }: { record: QuestionAccuracyStats }) => {
      return `${(record.average_accuracy * 100).toFixed(1)}%`
    },
  },
  {
    title: '参与统计试卷数',
    dataIndex: 'total_sheets',
    width: 150,
  },
  {
    title: '小题详情',
    slotName: 'sub_questions',
  },
]

// 题目批改结果表格列配置
const resultsColumns = [
  {
    title: '学号',
    dataIndex: 'student_number',
    width: 120,
  },
  {
    title: '大题题号',
    dataIndex: 'main_question',
    width: 100,
  },
  {
    title: '小题题号',
    dataIndex: 'sub_question',
    width: 100,
  },
  {
    title: '得分',
    slotName: 'score',
    width: 80,
  },
  {
    title: '匹配程度',
    slotName: 'right',
    width: 150,
  },
  {
    title: '给分原因',
    dataIndex: 'reason',
    ellipsis: true,
    tooltip: true,
  },
]

// 获取题目正确率统计
const fetchAccuracyStats = async () => {
  if (!taskId.value) return

  accuracyLoading.value = true
  try {
    const res = await getQuestionAccuracyStats(taskId.value)
    accuracyStats.value = res.list
    
    // 自动选择第一个大题
    if (res.list.length > 0 && !selectedMainQuestion.value) {
      selectedMainQuestion.value = res.list[0].main_question
      await fetchQuestionResults()
    }
  } catch (error) {
    Message.error('获取题目正确率统计失败')
    console.error(error)
  } finally {
    accuracyLoading.value = false
  }
}

// 获取题目批改结果
const fetchQuestionResults = async () => {
  if (!taskId.value || !selectedMainQuestion.value) return

  resultsLoading.value = true
  try {
    const params: QuestionResultsRequest = {
      main_question: selectedMainQuestion.value,
      include_correct: includeCorrect.value,
    }
    
    const res = await getQuestionResults(taskId.value, params)
    questionResults.value = res.list
    resultsPagination.value.total = res.list.length
  } catch (error) {
    Message.error('获取题目批改结果失败')
    console.error(error)
  } finally {
    resultsLoading.value = false
  }
}

// 处理大题选择变化
const handleMainQuestionChange = () => {
  resultsPagination.value.current = 1
  fetchQuestionResults()
}

// 处理是否包含正确答案变化
const handleIncludeCorrectChange = () => {
  resultsPagination.value.current = 1
  fetchQuestionResults()
}

// 处理分页变化
const handlePageChange = (page: number) => {
  resultsPagination.value.current = page
}

// 返回任务详情
const goBack = () => {
  router.push({
    name: 'CorrectionTaskDetail',
    params: { taskId: taskId.value },
  })
}

// 页面加载时获取数据
onMounted(async () => {
  loading.value = true
  try {
    await fetchAccuracyStats()
  } finally {
    loading.value = false
  }
})
</script>

<style scoped lang="scss">
.container {
  padding: 20px;
}

.section {
  margin-bottom: 24px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--color-text-1);
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
}

.mb-1 {
  margin-bottom: 4px;
}

.mr-1 {
  margin-right: 4px;
}

.ml-2 {
  margin-left: 8px;
}

.text-gray {
  color: var(--color-text-3);
}
</style>
