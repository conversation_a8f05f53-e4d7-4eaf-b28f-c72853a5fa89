<template>
  <div class="container">
    <a-card class="general-card" :title="$route.meta.locale">
      <template #extra>
        <a-space>
          <a-button type="primary" status="warning" @click="handleTriggerCorrection">
            <template #icon><icon-sync /></template>
            重新批改此答题卡
          </a-button>
          <a-button
            type="primary"
            status="success"
            @click="showApplySegmentationModal"
            :disabled="sheet?.segmentation_status !== SegmentationStatus.MANUAL"
          >
            <template #icon><icon-check-circle /></template>
            应用切题结果
          </a-button>
          <a-button
            type="primary"
            @click="handleMarkSheet"
            :disabled="sheet?.status !== 'completed'"
          >
            <template #icon><icon-edit /></template>
            原卷留痕
          </a-button>
          <pdf-printer
            :load-url="fetchSheetMarkedPdfUrl"
            button-text="打印留痕PDF"
            button-status="warning"
            :disabled="sheet?.status !== 'completed'"
          />
          <a-button type="primary" @click="goBack">
            <template #icon><icon-arrow-left /></template>
            返回答题卡列表
          </a-button>
        </a-space>
      </template>

      <a-spin style="display: block" :loading="loading">
        <!-- 基本信息 -->
        <a-descriptions :data="basicInfo" :column="3" size="large" bordered />
        <!-- 批改结果 -->
        <div class="section">
          <div class="section-header">
            <h3 class="section-title">批改结果</h3>
            <a-button type="primary" @click="answerVisible = true"> 查看答案 </a-button>
          </div>
          <div v-if="sheet?.status === 'completed' && sheet?.correct_result">
            <div class="result-container">
              <a-descriptions :column="1" bordered>
                <a-descriptions-item label="学号">
                  {{ sheet.correct_result.student_number }}
                </a-descriptions-item>
                <a-descriptions-item label="总得分">
                  {{ getScoreDisplay(getTotalScore(sheet.correct_result.answer)) }}
                </a-descriptions-item>
              </a-descriptions>

              <div class="mt-4">
                <div class="questions-grid">
                  <div
                    v-for="item in sheet.correct_result.answer"
                    :key="getQuestionKey(item)"
                    class="question-box"
                    :class="getColorClass(item.right)"
                    @click="showQuestionDetail(item)"
                  >
                    {{ getQuestionDisplay(item) }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div v-else-if="sheet?.status === 'in_progress'">
            <a-result
              status="info"
              title="批改中"
              sub-title="系统正在批改中，请稍后刷新页面查看结果"
            />
          </div>

          <div v-else-if="sheet?.status === 'waiting'">
            <a-result
              status="warning"
              title="待批改"
              sub-title="系统尚未开始批改，请稍后刷新页面查看结果"
            />
          </div>

          <div v-else-if="sheet?.status === 'failed'">
            <a-result status="error" title="批改失败" sub-title="系统批改失败，请联系管理员" />
          </div>
          <div v-else>
            <a-result
              status="warning"
              :title="'未知状态' + sheet?.status"
              sub-title="请联系管理员"
            />
          </div>
        </div>
        <!-- 批改项列表 -->
        <div class="section">
          <div class="section-header">
            <h3 class="section-title">页面列表</h3>
            <a-button type="primary" @click="refresh">
              <template #icon><icon-refresh /></template>
              刷新
            </a-button>
          </div>
          <a-table
            :loading="itemsLoading"
            :pagination="itemsPagination"
            :data="itemsData"
            :bordered="false"
            @page-change="onItemsPageChange"
          >
            <template #columns>
              <a-table-column title="ID" data-index="item_id" :width="150" />
              <a-table-column title="序号" data-index="sequence_number" :width="50" />
              <a-table-column title="图片" :width="50">
                <template #cell="{ record }">
                  <a-image :width="50" :src="record.file_url" />
                </template>
              </a-table-column>
              <a-table-column title="操作" :width="50" fixed="right">
                <template #cell="{ record }">
                  <a-space>
                    <a-button type="text" size="small" @click="viewItemDetail(record)">
                      查看详情
                    </a-button>
                    <a-button
                      type="text"
                      size="small"
                      status="success"
                      @click="openSegmentationModal(record)"
                    >
                      切题
                    </a-button>
                  </a-space>
                </template>
              </a-table-column>
            </template>
          </a-table>
        </div>
      </a-spin>
    </a-card>

    <!-- 切题弹窗 -->
    <SegmentationModal
      v-model:visible="segmentationModalVisible"
      :sheet-id="sheetId"
      :item-id="currentItemId"
      :answer-image-url="currentAnswerImageUrl"
      :student-image-url="currentStudentImageUrl"
      @saved="handleSegmentationSaved"
    />

    <!-- 应用切题结果弹窗 -->
    <a-modal
      v-model:visible="applySegmentationModalVisible"
      title="应用切题结果"
      @cancel="applySegmentationModalVisible = false"
      @before-ok="handleApplySegmentation"
    >
      <p>确定要将当前答题卡的切题结果应用到整个批改任务吗？</p>
      <p>这将会把当前答题卡的切题结果应用到所有其他答题卡上。</p>
    </a-modal>

    <!-- 题目详情弹窗 -->
    <a-modal
      v-model:visible="questionDetailVisible"
      :title="`题目 ${getQuestionDisplay(currentQuestion) || ''} 详情`"
      @cancel="closeQuestionDetail"
      :footer="false"
      :width="600"
    >
      <div v-if="currentQuestion" class="question-detail">
        <a-descriptions :column="1" bordered layout="vertical">
          <a-descriptions-item label="得分">
            <a-tag :color="getScoreColor(currentQuestion.right)" size="medium">{{
              getScoreDisplay(currentQuestion.score)
            }}</a-tag>
          </a-descriptions-item>
          <!-- <a-descriptions-item label="学生作答">
            <div class="answer-content">{{ currentQuestion.answer }}</div>
          </a-descriptions-item> -->
          <a-descriptions-item label="给分原因">
            <div class="reason-content">{{ currentQuestion.reason }}</div>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
    <a-image-preview-group v-model:visible="answerVisible" :srcList="task?.answer_file_urls" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Message, Modal } from '@arco-design/web-vue'
import { IconSync, IconCheckCircle, IconEdit } from '@arco-design/web-vue/es/icon'
import {
  getSheetDetail,
  getSheetItems,
  applySheetSegmentation,
  triggerCorrection,
  markSheet,
  getCorrectionTaskDetail,
  getSheetMarkedPDF,
  type UploadQueueItem,
  type ListCorrectionItemsRequest,
  type CorrectionTaskDTO,
  CorrectResultItem,
  SheetDetail,
  // type MarkSheetResponse,
} from '@/api/correction'
import SegmentationModal from '@/components/segmentation/SegmentationModal.vue'
import { SegmentationStatus, type ApplySegmentationRequest } from '@/components/segmentation/model'

const route = useRoute()
const router = useRouter()
const sheetId = computed(() => route.params.sheetId as string)

const loading = ref(false)
const taskLoading = ref(false)
const sheet = ref<SheetDetail | null>(null)
const task = ref<CorrectionTaskDTO | null>(null)

const answerVisible = ref(false)

// 批改项列表相关状态
const itemsLoading = ref(false)
const itemsData = ref<UploadQueueItem[]>([])
const itemsPagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
})

// 切题相关状态
const segmentationModalVisible = ref(false)
const currentItemId = ref('')
const currentAnswerImageUrl = ref('')
const currentStudentImageUrl = ref('')
const applySegmentationModalVisible = ref(false)
const applyingSegmentation = ref(false)
const questionDetailVisible = ref(false)
const currentQuestion = ref<CorrectResultItem | null>(null)

// 基本信息
const basicInfo = computed(() => {
  if (!sheet.value) return []

  return [
    {
      label: 'ID',
      value: sheet.value.id,
    },
    {
      label: '学号',
      value: sheet.value.student_number,
    },
    {
      label: '包含页面数',
      value: sheet.value.item_count + '',
    },
    {
      label: '创建时间',
      value: formatDate(sheet.value.created_at),
    },
  ]
})

// 获取分数显示文本，负数显示为"无评分标准"
const getScoreDisplay = (score: number) => {
  return score >= 0 ? `${score}分` : '无评分标准'
}

const getColorClass = (right: number) => {
  if (right === 1) return 'question-box-green'
  if (right === 0) return 'question-box-red'
  return 'question-box-yellow'
}

const getTotalScore = (output: CorrectResultItem[] | undefined) => {
  if (!output) return 0
  return output.reduce((total, item) => total + item.score, 0)
}

// 获取题目的唯一键
const getQuestionKey = (question: CorrectResultItem | null): string => {
  if (!question) return ''
  return `${question.main_question}-${question.sub_question}`
}

// 获取题目的显示文本
const getQuestionDisplay = (question: CorrectResultItem | null): string => {
  if (!question) return ''
  if (question.sub_question) {
    return `${question.main_question}${question.sub_question}`
  }
  return question.main_question
}

const showQuestionDetail = (question: CorrectResultItem) => {
  currentQuestion.value = question
  questionDetailVisible.value = true
}

const closeQuestionDetail = () => {
  questionDetailVisible.value = false
  currentQuestion.value = null
}

const getScoreColor = (right: number) => {
  if (right === 1) return 'green'
  if (right === 0) return 'red'
  return 'orange'
}

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return ''
  const d = new Date(date)
  return d.toLocaleString()
}

// 获取答题卡详情
const fetchSheetDetail = async () => {
  if (!sheetId.value) return

  loading.value = true
  try {
    const res = await getSheetDetail(sheetId.value)
    sheet.value = res
  } catch (error) {
    Message.error('获取答题卡详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 获取任务详情
const fetchTaskDetail = async () => {
  if (!sheet.value?.task_id) return

  taskLoading.value = true
  try {
    const res = await getCorrectionTaskDetail(sheet.value.task_id)
    task.value = res
  } catch (error) {
    Message.error('获取批改任务详情失败')
    console.error(error)
  } finally {
    taskLoading.value = false
  }
}

// 获取答题卡留痕PDF URL
const fetchSheetMarkedPdfUrl = async () => {
  if (!sheetId.value) return

  try {
    const res = await getSheetMarkedPDF(sheetId.value)
    return res.url || ''
  } catch (error) {
    console.error('获取答题卡留痕PDF URL失败:', error)
    return ''
  }
}

// 获取批改项列表
const fetchItems = async () => {
  if (!sheetId.value) return

  itemsLoading.value = true
  try {
    const params: ListCorrectionItemsRequest = {
      page: itemsPagination.current,
      size: itemsPagination.pageSize,
    }
    const { data, total } = await getSheetItems(sheetId.value, params)
    itemsData.value = data
    itemsPagination.total = total
  } catch (error) {
    Message.error('获取批改项列表失败')
    console.error(error)
  } finally {
    itemsLoading.value = false
  }
}

// 批改项分页变化
const onItemsPageChange = (current: number) => {
  itemsPagination.current = current
  fetchItems()
}

// 查看批改项详情
const viewItemDetail = (record: UploadQueueItem) => {
  router.push({
    name: 'CorrectionItemDetail',
    params: { itemId: record.item_id },
  })
}

// 打开切题弹窗
const openSegmentationModal = (record: UploadQueueItem) => {
  currentItemId.value = record.item_id
  currentAnswerImageUrl.value = record.answer_file_url || ''
  currentStudentImageUrl.value = record.file_url
  segmentationModalVisible.value = true
}

// 处理切题保存成功
const handleSegmentationSaved = () => {
  // 刷新数据
  fetchItems()
}

// 显示应用切题结果弹窗
const showApplySegmentationModal = () => {
  applySegmentationModalVisible.value = true
}

// 应用切题结果
const handleApplySegmentation = async (done: (closed: boolean) => void) => {
  if (!sheetId.value) {
    done(false)
    return
  }

  applyingSegmentation.value = true
  try {
    // 获取当前任务ID
    const taskId = sheet.value?.task_id
    if (!taskId) {
      Message.error('无法获取当前任务ID')
      done(false)
      return
    }

    const data: ApplySegmentationRequest = {
      reference_sheet_id: sheetId.value,
    }

    await applySheetSegmentation(taskId, data)
    Message.success('应用切题结果成功')
    fetchItems() // 刷新数据
    done(true)
  } catch (error) {
    console.error('应用切题结果失败:', error)
    Message.error('应用切题结果失败')
    done(false)
  } finally {
    applyingSegmentation.value = false
  }
}

// 处理原卷留痕
const handleMarkSheet = async () => {
  if (!sheetId.value) return

  Modal.confirm({
    title: '确认原卷留痕',
    content: '您确定要对此答题卡进行原卷留痕吗？',
    onOk: async () => {
      try {
        loading.value = true
        const res = await markSheet(sheetId.value)

        if (res.marked_count > 0) {
          Message.success(`原卷留痕成功，共标记了 ${res.marked_count} 个项目`)
          // 刷新数据
          refresh()
        } else {
          Message.warning('没有项目被标记')
        }
      } catch (error) {
        Message.error('原卷留痕失败')
        console.error(error)
      } finally {
        loading.value = false
      }
    },
  })
}

// 触发重新批改
const handleTriggerCorrection = async () => {
  if (!sheetId.value) return

  // 获取任务ID
  const taskId =
    (route.query.taskId as string) || (route.params.taskId as string) || sheet.value?.task_id
  if (!taskId) {
    Message.error('无法获取任务ID')
    return
  }

  // 进行批改
  Modal.confirm({
    title: '确认重新批改',
    content: '您确定要重新批改此答题卡吗？',
    onOk: async () => {
      try {
        await triggerCorrection(taskId, {
          sheet_ids: [sheetId.value],
        })
        Message.success('成功触发批改')
        // 刷新数据
        refresh()
      } catch (error) {
        Message.error('触发批改失败')
        console.error(error)
      }
    },
  })
}

// 返回列表
const goBack = () => {
  router.back()
}

// 刷新数据
const refresh = async () => {
  await fetchSheetDetail()
  fetchItems()
  if (sheet.value?.task_id) {
    fetchTaskDetail()
  }
}

onMounted(async () => {
  await fetchSheetDetail()
  fetchItems()
  if (sheet.value?.task_id) {
    fetchTaskDetail()
  }
})
</script>

<style scoped lang="less">
.container {
  padding: 0 20px 20px 20px;
}

.general-card {
  margin-top: 20px;
}

.section {
  margin-top: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 0;
}

.questions-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 12px;
}

.question-box {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    opacity: 0.8;
    transform: translateY(-2px);
  }
}

.question-box-green {
  background-color: #00b42a;
}

.question-box-yellow {
  background-color: #ff7d00;
}

.question-box-red {
  background-color: #f53f3f;
}

.question-detail {
  padding: 10px;
}

.full-size-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  max-height: 80vh;
  overflow: auto;
}

.full-size-image {
  max-width: 100%;
  max-height: 100%;
}
</style>
