<template>
  <div class="container">
    <a-card class="general-card" :title="$route.meta.locale">
      <template #extra>
        <a-button type="primary" @click="goBack">
          <template #icon><icon-arrow-left /></template>
          返回任务详情
        </a-button>
      </template>

      <a-row class="mb-4">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" @click="refresh">
              <template #icon><icon-refresh /></template>
              刷新
            </a-button>
            <a-button
              type="primary"
              status="warning"
              :disabled="selectedSheets.length === 0"
              @click="handleTriggerCorrection"
            >
              <template #icon><icon-sync /></template>
              重新批改选中答题卡
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        :loading="loading"
        :pagination="pagination"
        :data="renderData"
        :bordered="false"
        row-key="id"
        :row-selection="{
          type: 'checkbox',
          showCheckedAll: true,
          onlyCurrent: false,
        }"
        v-model:selected-keys="selectedSheets"
        @page-change="onPageChange"
      >
        <template #columns>
          <a-table-column title="ID" data-index="id" :width="100" />
          <a-table-column title="学号" data-index="student_number" :width="150" />
          <a-table-column title="包含项目数量" data-index="item_count" :width="120" />
          <a-table-column title="创建时间" :width="180">
            <template #cell="{ record }">
              {{ formatDate(record.created_at) }}
            </template>
          </a-table-column>
          <a-table-column title="操作" :width="150" fixed="right">
            <template #cell="{ record }">
              <a-space>
                <a-button type="text" size="small" @click="viewSheetDetail(record)">
                  查看详情
                </a-button>
              </a-space>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Message, Modal } from '@arco-design/web-vue'
import { IconSync } from '@arco-design/web-vue/es/icon'
import {
  getSheets,
  triggerCorrection,
  getCorrectionTaskDetail,
  reParseContent,
  PARSE_STATUS,
} from '@/api/correction'
import type { SheetDTO, CorrectionTaskDTO } from '@/api/correction'

const route = useRoute()
const router = useRouter()
const taskId = computed(() => route.params.taskId as string)

const loading = ref(false)
const taskLoading = ref(false)
const renderData = ref<SheetDTO[]>([])
const task = ref<CorrectionTaskDTO | null>(null)
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
})

// 选中的答题卡
const selectedSheets = ref<string[]>([])

const fetchData = async () => {
  if (!taskId.value) return

  loading.value = true
  try {
    const { data, total } = await getSheets(taskId.value, {
      page: pagination.current,
      size: pagination.pageSize,
    })
    renderData.value = data
    pagination.total = total
  } catch (error) {
    Message.error('获取答题卡列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 获取任务详情
const fetchTaskDetail = async () => {
  if (!taskId.value) return

  taskLoading.value = true
  try {
    const res = await getCorrectionTaskDetail(taskId.value)
    task.value = res
  } catch (error) {
    Message.error('获取批改任务详情失败')
    console.error(error)
  } finally {
    taskLoading.value = false
  }
}

const refresh = () => {
  fetchData()
  fetchTaskDetail()
}

const onPageChange = (current: number) => {
  pagination.current = current
  fetchData()
}

const formatDate = (date: string) => {
  if (!date) return ''
  const d = new Date(date)
  return d.toLocaleString()
}

const viewSheetDetail = (record: SheetDTO) => {
  router.push({
    name: 'CorrectionSheetDetail',
    params: { sheetId: record.id },
  })
}

const goBack = () => {
  router.push({
    name: 'CorrectionTaskDetail',
    params: { taskId: taskId.value },
  })
}

// 触发批改
const handleTriggerCorrection = async () => {
  if (selectedSheets.value.length === 0) {
    Message.warning('请选择需要重新批改的答题卡')
    return
  }
  // 答案已解析完成，可以进行批改
  Modal.confirm({
    title: '确认重新批改',
    content: `您确定要重新批改选中的 ${selectedSheets.value.length} 个答题卡吗？`,
    onOk: async () => {
      try {
        const response = await triggerCorrection(taskId.value, {
          sheet_ids: selectedSheets.value,
        })
        Message.success(`成功触发 ${response.count} 个答题卡的批改`)
        // 刷新数据
        fetchData()
        // 清空选择
        selectedSheets.value = []
      } catch (error) {
        Message.error('触发批改失败')
        console.error(error)
      }
    },
  })
}

onMounted(() => {
  fetchData()
  fetchTaskDetail()
})
</script>

<style scoped lang="less">
.container {
  padding: 0 20px 20px 20px;
}

.general-card {
  margin-top: 20px;
}

.mb-4 {
  margin-bottom: 16px;
}
</style>
