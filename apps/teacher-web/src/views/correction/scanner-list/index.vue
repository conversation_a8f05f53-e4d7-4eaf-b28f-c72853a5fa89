<template>
  <div class="container">
    <a-card class="general-card" :title="$route.meta.locale">
      <template #extra>
        <a-button type="primary" @click="goBack">
          <template #icon><IconArrowLeft /></template>
          返回任务详情
        </a-button>
      </template>
      <a-row class="mb-4">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" @click="refresh">
              <template #icon><IconRefresh /></template>
              刷新
            </a-button>
          </a-space>
        </a-col>
      </a-row>
      <a-table
        :loading="loading"
        :pagination="pagination"
        :data="renderData"
        :bordered="false"
        @page-change="onPageChange"
      >
        <template #columns>
          <a-table-column title="ID" data-index="printerId" :width="100" />
          <a-table-column title="状态" data-index="status" :width="120">
            <template #cell="{ record }">
              <a-tag :color="getStatusColor(record.status)">
                {{ getStatusText(record.status) }}
              </a-tag>
            </template>
          </a-table-column>
          <a-table-column title="过期时间" data-index="expiredAt" :width="120" />
          <a-table-column title="扫描时间" data-index="scannedAt" :width="120" />
          <a-table-column title="绑定时间" data-index="bindAt" :width="120" />
          <a-table-column title="操作" :width="120">
            <template #cell="{ record }">
              <a-button type="text" size="small" @click="handleDelete(record)"> 删除 </a-button>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, reactive, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Message, Modal } from '@arco-design/web-vue'
import { IconArrowLeft, IconRefresh } from '@arco-design/web-vue/es/icon'
import { listTaskPrinters } from '@/api/correction'
import { deleteTaskPrinter, ListPrinterResponse } from '@/api/correction'

const route = useRoute()
const router = useRouter()
const taskId = computed(() => route.params.taskId as string)

const loading = ref(false)
const renderData = ref<ListPrinterResponse[]>([])
const pagination = reactive({
  total: 0,
  current: 1,
  pageSize: 10,
})

const fetchData = async () => {
  if (!taskId.value) return

  loading.value = true
  try {
    const res = await listTaskPrinters(taskId.value)
    renderData.value = res
    pagination.total = res.length
  } catch (error) {
    Message.error('获取扫描仪列表失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const refresh = () => {
  fetchData()
}

const onPageChange = (current: number) => {
  pagination.current = current
  fetchData()
}

const getStatusColor = (status: string) => {
  const statusMap: Record<string, string> = {
    scanned: 'blue',
    bind: 'green',
  }
  return statusMap[status] || 'default'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    scanned: '已扫描',
    bind: '已绑定',
  }
  return statusMap[status] || '未知'
}

const handleDelete = async (record: ListPrinterResponse) => {
  Modal.warning({
    title: '警告',
    content: `你正在删除关联扫描仪，删除后不可恢复，是否继续？`,
    hideCancel: false,
    onOk() {
      deleteTaskPrinter(taskId.value, record.printerId)
        .then(() => {
          Message.success('删除成功')
          void fetchData()
        })
        .catch((err) => {
          console.error(err)
          Message.error('删除失败')
        })
    },
  })
}

const goBack = () => {
  router.push({
    name: 'CorrectionTaskDetail',
    params: { taskId: taskId.value },
  })
}

onMounted(() => {
  fetchData()
})
</script>

<style scoped lang="less">
.container {
  padding: 0 20px 20px 20px;
}

.general-card {
  margin-top: 20px;
}

.mb-4 {
  margin-bottom: 16px;
}

.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-height: 80vh;
  overflow: auto;
}

.preview-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
}
</style>
