<template>
  <div class="container">
    <a-card class="general-card" :title="$route.meta.locale">
      <template #extra>
        <a-button type="primary" @click="goBack">
          <template #icon><icon-arrow-left /></template>
          返回列表
        </a-button>
      </template>
      <a-row class="mb-4">
        <a-col :span="12">
          <a-space>
            <a-button type="primary" @click="viewSheets">
              <template #icon><icon-file /></template>
              查看全部答题卡
            </a-button>
            <a-button type="primary" status="success" @click="markSheet">
              <template #icon><icon-file-pdf /></template>
              原卷留痕
            </a-button>
            <pdf-printer
              :load-url="fetchMarkedPdfUrl"
              button-text="打印留痕PDF"
              button-status="warning"
              :disabled="!markedPdfUrl"
            />
            <a-button type="primary" status="normal" @click="viewCorrectionReport">
              <template #icon><icon-bar-chart /></template>
              查看项目批改结果
            </a-button>
          </a-space>
        </a-col>
      </a-row>

      <a-spin style="display: block" :loading="loading">
        <!-- 进度信息 -->
        <div class="section">
          <div class="progress-section">
            <div class="progress-info">
              <div class="progress-item">
                <div class="progress-label">已扫描页数</div>
                <div class="progress-value">{{ task?.total_sheets || 0 }}</div>
              </div>
              <div class="progress-item">
                <div class="progress-label">已批改页数</div>
                <div class="progress-value">{{ task?.corrected_sheets || 0 }}</div>
              </div>
              <div class="progress-item">
                <div class="progress-label">正在批改中</div>
                <div class="progress-value">{{ task?.in_progress_sheets || 0 }}</div>
              </div>
              <div class="progress-item">
                <div class="progress-label">批改失败</div>
                <div class="progress-value">{{ task?.failed_sheets || 0 }}</div>
              </div>
            </div>
            <div class="progress-bar-container">
              <div class="progress-bar-with-info">
                <a-progress
                  :percent="getProgressPercent()"
                  :status="getProgressStatus()"
                  :stroke-width="20"
                  :show-text="false"
                />
              </div>
            </div>
          </div>
        </div>

        <!-- 基本信息 -->
        <div class="section">
          <div class="section-header">
            <h3 class="section-title">基本信息</h3>
            <a-button type="primary" @click="showSettingsModal">编辑配置</a-button>
          </div>

          <a-descriptions :data="basicInfo" :column="3" size="large" bordered />
        </div>

        <!-- 配置信息 -->
        <div class="section">
          <h3 class="section-title">批改配置</h3>
          <a-descriptions :data="configInfo" :column="3" bordered />
        </div>

        <!-- 试卷和答案文件 -->
        <div class="section" v-if="hasPaperOrAnswerFiles">
          <div class="section-header">
            <h3 class="section-title">试卷和答案</h3>
            <div class="header-btn">
              <a-space>
                <a-button
                  type="primary"
                  @click="handleReParsePaper"
                  :loading="paperParseLoading"
                  :status="getPaperParseButtonStatus()"
                  v-if="(task?.paper_file_urls?.length || 0) > 0"
                >
                  <template #icon><icon-refresh /></template>
                  {{ getPaperParseButtonText() }}
                </a-button>
                <a-button
                  type="primary"
                  @click="handleReParseAnswer"
                  :loading="answerParseLoading"
                  :status="getAnswerParseButtonStatus()"
                  v-if="(task?.answer_file_urls?.length || 0) > 0"
                >
                  <template #icon><icon-refresh /></template>
                  {{ getAnswerParseButtonText() }}
                </a-button>
              </a-space>
            </div>
          </div>
          <div class="files-section">
            <!-- 试卷文件 -->
            <div class="file-group" v-if="task?.paper_file_urls?.length">
              <h4 class="file-group-title">试卷文件</h4>
              <div class="file-list">
                <a-image-preview-group>
                  <a-space>
                    <div
                      v-for="(url, index) in task.paper_file_urls"
                      :key="`paper-${index}`"
                      class="file-item"
                    >
                      <div class="file-preview">
                        <a-image :width="200" :src="url" />
                      </div>
                    </div>
                  </a-space>
                </a-image-preview-group>
              </div>
            </div>

            <!-- 答案文件 -->
            <div class="file-group" v-if="task?.answer_file_urls?.length">
              <h4 class="file-group-title">参考答案</h4>
              <div class="file-list">
                <a-image-preview-group>
                  <a-space>
                    <div
                      v-for="(url, index) in task.answer_file_urls"
                      :key="`answer-${index}`"
                      class="file-item"
                    >
                      <div class="file-preview">
                        <a-image :width="200" :src="url" />
                      </div>
                    </div>
                  </a-space>
                </a-image-preview-group>
              </div>
            </div>
          </div>
        </div>
      </a-spin>

      <!-- 分组设置对话框 -->
      <a-modal
        v-model:visible="groupingModalVisible"
        title="答题卡分组设置"
        @cancel="closeGroupingModal"
        @before-ok="submitGrouping"
        :ok-loading="groupingLoading"
      >
        <a-form :model="groupingForm" layout="vertical">
          <a-form-item field="has_student_number" label="学号信息">
            <a-switch v-model="groupingForm.has_student_number" />
            <div class="form-item-help">是否每张答题卡都有学号信息</div>
          </a-form-item>
          <a-form-item field="force_by_sheets" label="强制按纸张数量分组">
            <a-switch v-model="groupingForm.force_by_sheets" />
            <div class="form-item-help">开启后将强制按纸张数量分组，而不是根据学号分组</div>
          </a-form-item>
        </a-form>
      </a-modal>

      <!-- 任务设置对话框 -->
      <task-settings-dialog
        v-model:visible="settingsModalVisible"
        :is-edit="true"
        :task="task"
        @submit="handleSettingsSubmit"
      />
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { IconFilePdf, IconRefresh, IconBarChart, IconFile, IconArrowLeft } from '@arco-design/web-vue/es/icon'
import {
  getCorrectionTaskDetail,
  groupCorrectionItems,
  getMarkedPDF,
  markTask,
  reParseContent,
  updateTaskSettings,
  type CorrectionTaskDTO,
  type GroupCorrectionItemsRequest,
  type UpdateTaskSettingsRequest,
  PARSE_STATUS,
  // type ReParseContentRequest,
  // type GetMarkedPDFResponse,
  // type MarkTaskResponse,
} from '@/api/correction'
import TaskSettingsDialog from '@/components/task-settings-dialog/index.vue'
import PdfPrinter from '@/components/pdf-printer/index.vue'

const route = useRoute()
const router = useRouter()
const taskId = computed(() => route.params.taskId as string)

const loading = ref(false)
const task = ref<CorrectionTaskDTO | null>(null)
const paperParseLoading = ref(false)
const answerParseLoading = ref(false)
const markedPdfUrl = ref<string>('')

// 获取原卷留痕PDF URL
const fetchMarkedPdfUrl = async () => {
  if (!taskId.value) return

  try {
    const res = await getMarkedPDF(taskId.value)
    return res.url || ''
  } catch (error) {
    console.error('获取原卷留痕PDF URL失败:', error)
    return ''
  }
}

// 分组相关状态
const groupingModalVisible = ref(false)
const groupingLoading = ref(false)
const groupingForm = reactive<GroupCorrectionItemsRequest>({
  has_student_number: true,
  force_by_sheets: false,
})

// 设置相关状态
const settingsModalVisible = ref(false)

// 基本信息
const basicInfo = computed(() => {
  if (!task.value) return []

  return [
    {
      label: 'ID',
      value: task.value.id,
    },
    {
      label: '标题',
      value: task.value.title,
    },
    {
      label: '状态',
      value: getStatusText(task.value.status),
    },

    {
      label: '创建时间',
      value: formatDate(task.value.created_at),
    },
    {
      label: '更新时间',
      value: formatDate(task.value.updated_at),
    },
  ]
})

// 配置信息
const configInfo = computed(() => {
  if (!task.value) return []

  return [
    {
      label: '是否需要扫描空白试卷',
      value: task.value.need_scan_paper ? '是' : '否',
    },
    {
      label: '是否在原卷上作答',
      value: task.value.paper_with_answer ? '是' : '否',
    },
    {
      label: '每张答题卡包含的页面数',
      value: task.value.sheets_per_paper + '',
    },
    {
      label: '是否双面打印',
      value: task.value.double_sided ? '是' : '否',
    },
    {
      label: '放入方向',
      value: task.value.vertical_arrangement ? '正常方向' : '垂直放入',
    },
  ]
})

// 获取任务状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    waiting_for_settings: '待完成设置',
    waiting_for_paper: '等待扫描试卷',
    waiting_for_answer: '等待扫描答案',
    scanning: '扫描答题卡中',
    completed: '已完成批改',
  }
  return statusMap[status] || status
}

// 获取进度百分比
const getProgressPercent = () => {
  if (!task.value || task.value.total_sheets === 0) return 0
  return Math.floor((task.value.corrected_sheets / task.value.total_sheets) * 100)
}

// 获取进度状态
const getProgressStatus = () => {
  if (!task.value) return 'normal'

  const percent = getProgressPercent()
  if (percent === 100 || task.value.status === 'completed') return 'success'
  if (task.value.status === 'scanning') return 'warning'
  if (
    task.value.status === 'waiting_for_settings' ||
    task.value.status === 'waiting_for_paper' ||
    task.value.status === 'waiting_for_answer'
  )
    return 'normal'
  return 'normal'
}

// 判断是否有试卷或答案文件
const hasPaperOrAnswerFiles = computed(() => {
  return (
    (task.value?.paper_file_urls && task.value.paper_file_urls.length > 0) ||
    (task.value?.answer_file_urls && task.value.answer_file_urls.length > 0)
  )
})

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return ''
  const d = new Date(date)
  return d.toLocaleString()
}

// 获取任务详情
const fetchTaskDetail = async () => {
  if (!taskId.value) return

  loading.value = true
  try {
    const res = await getCorrectionTaskDetail(taskId.value)
    task.value = res
  } catch (error) {
    Message.error('获取批改任务详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 返回列表
const goBack = () => {
  router.push({
    name: 'CorrectionTaskList',
  })
}
// 关闭分组对话框
const closeGroupingModal = () => {
  groupingModalVisible.value = false
}

// 提交分组请求
const submitGrouping = async () => {
  if (!taskId.value) return

  groupingLoading.value = true
  try {
    await groupCorrectionItems(taskId.value, groupingForm)
    Message.success('分组请求已提交，正在处理中')
    closeGroupingModal()
    fetchTaskDetail() // 刷新任务状态
  } catch (error) {
    Message.error('提交分组请求失败')
    console.error(error)
  } finally {
    groupingLoading.value = false
  }
}

// 查看答题卡列表
const viewSheets = () => {
  router.push({
    name: 'CorrectionSheetsList',
    params: { taskId: taskId.value },
  })
}

// 查看项目批改结果
const viewCorrectionReport = () => {
  router.push({
    name: 'CorrectionReport',
    params: { taskId: taskId.value },
  })
}

// 触发原卷留痕操作
const triggerMarkTask = async (): Promise<boolean> => {
  if (!taskId.value) return false

  try {
    loading.value = true
    const markResponse = await markTask(taskId.value)
    if (markResponse.marked_count > 0) {
      Message.success(`成功标记 ${markResponse.marked_count} 份答题卡`)
      return true
    } else {
      Message.warning('没有可标记的答题卡')
      return false
    }
  } catch (error) {
    Message.error('触发原卷留痕失败')
    console.error(error)
    return false
  } finally {
    loading.value = false
  }
}

// 查看原卷留痕PDF
const markSheet = async () => {
  if (!taskId.value) return

  try {
    loading.value = true

    // 触发原卷留痕操作
    const markSuccess = await triggerMarkTask()
    if (!markSuccess) return
  } catch (error) {
    Message.error('获取原卷留痕PDF失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 获取试卷解析按钮文本
const getPaperParseButtonText = () => {
  if (!task.value) return '重新解析试卷'

  switch (task.value.paper_parse_status) {
    case PARSE_STATUS.WAITING:
      return '解析试卷'
    case PARSE_STATUS.IN_PROGRESS:
      return '试卷解析中'
    case PARSE_STATUS.COMPLETED:
      return '重新解析试卷'
    case PARSE_STATUS.FAILED:
      return '重新解析试卷'
    default:
      return '解析试卷'
  }
}

// 获取答案解析按钮文本
const getAnswerParseButtonText = () => {
  if (!task.value) return '重新解析答案'

  switch (task.value.answer_parse_status) {
    case PARSE_STATUS.WAITING:
      return '解析答案'
    case PARSE_STATUS.IN_PROGRESS:
      return '答案解析中'
    case PARSE_STATUS.COMPLETED:
      return '重新解析答案'
    case PARSE_STATUS.FAILED:
      return '重新解析答案'
    default:
      return '解析答案'
  }
}

// 获取试卷解析按钮状态
const getPaperParseButtonStatus = () => {
  if (!task.value) return 'normal'

  switch (task.value.paper_parse_status) {
    case PARSE_STATUS.IN_PROGRESS:
      return 'warning'
    case PARSE_STATUS.COMPLETED:
      return 'success'
    case PARSE_STATUS.FAILED:
      return 'danger'
    default:
      return 'normal'
  }
}

// 获取答案解析按钮状态
const getAnswerParseButtonStatus = () => {
  if (!task.value) return 'normal'

  switch (task.value.answer_parse_status) {
    case PARSE_STATUS.IN_PROGRESS:
      return 'warning'
    case PARSE_STATUS.COMPLETED:
      return 'success'
    case PARSE_STATUS.FAILED:
      return 'danger'
    default:
      return 'normal'
  }
}

// 处理重新解析试卷
const handleReParsePaper = async () => {
  if (!taskId.value) return

  // 如果正在解析中，不允许再次触发
  if (task.value?.paper_parse_status === PARSE_STATUS.IN_PROGRESS) {
    Message.warning('试卷正在解析中，请稍后再试')
    return
  }

  try {
    paperParseLoading.value = true
    await reParseContent(taskId.value, { type: 'paper' })
    Message.success('已触发试卷解析，请稍后刷新查看结果')
    // 刷新任务详情
    await fetchTaskDetail()
  } catch (error) {
    console.error('重新解析试卷失败:', error)
    Message.error('重新解析试卷失败')
  } finally {
    paperParseLoading.value = false
  }
}

// 处理重新解析答案
const handleReParseAnswer = async () => {
  if (!taskId.value) return

  // 如果正在解析中，不允许再次触发
  if (task.value?.answer_parse_status === PARSE_STATUS.IN_PROGRESS) {
    Message.warning('答案正在解析中，请稍后再试')
    return
  }

  try {
    answerParseLoading.value = true
    await reParseContent(taskId.value, { type: 'answer' })
    Message.success('已触发答案解析，请稍后刷新查看结果')
    // 刷新任务详情
    await fetchTaskDetail()
  } catch (error) {
    console.error('重新解析答案失败:', error)
    Message.error('重新解析答案失败')
  } finally {
    answerParseLoading.value = false
  }
}

// 显示设置对话框
const showSettingsModal = () => {
  settingsModalVisible.value = true
}

// 处理设置提交
const handleSettingsSubmit = async (data: UpdateTaskSettingsRequest) => {
  if (!taskId.value) return

  try {
    loading.value = true
    await updateTaskSettings(taskId.value, data)
    Message.success('任务设置更新成功')
    // 刷新任务详情
    await fetchTaskDetail()
    // 关闭对话框
    settingsModalVisible.value = false
  } catch (error) {
    Message.error('更新任务设置失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchTaskDetail()
})
</script>

<style scoped lang="less">
.container {
  padding: 0 20px 20px 20px;
}

.general-card {
  margin-top: 20px;
}

.section {
  margin-top: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.header-btn {
  display: flex;
  gap: 8px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
  margin-bottom: 0;
}

.progress-section {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 20px;
}

.progress-info {
  display: flex;
  margin-bottom: 20px;
}

.progress-item {
  flex: 1;
  text-align: center;
}

.progress-label {
  font-size: 14px;
  color: #4e5969;
  margin-bottom: 8px;
}

.progress-value {
  font-size: 24px;
  font-weight: 500;
  color: #1d2129;
}

.progress-bar-container {
  padding: 0 20px;
}

.progress-bar-with-info {
  position: relative;
}

.progress-bar-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.progress-percent {
  font-size: 16px;
  font-weight: 500;
  color: #1d2129;
}

.progress-detail {
  font-size: 14px;
  color: #4e5969;
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-row {
  margin-bottom: 16px;
}

.grouping-section {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.grouping-status {
  display: flex;
  align-items: center;
}

.status-label {
  margin-right: 8px;
  font-size: 14px;
  color: #4e5969;
}

.form-item-help {
  font-size: 12px;
  color: #86909c;
  margin-top: 4px;
}

.files-section {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 20px;
}

.file-group {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.file-group-title {
  font-size: 15px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #1d2129;
}

.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.file-item {
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;
}

.file-preview {
  height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background-color: #f2f3f5;
  cursor: pointer;

  &:hover {
    background-color: #e8e9eb;
  }
}

.preview-thumbnail {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.file-actions {
  padding: 8px 12px;
  border-top: 1px solid #e5e6eb;
  text-align: center;
}
</style>
