<template>
  <div class="container">
    <a-card class="general-card" :title="$route.meta.locale">
      <template #extra>
        <a-space>
          <a-button v-if="detail?.status === 'completed'" type="primary" @click="handleMarkItem">
            <template #icon><icon-edit /></template>
            原卷留痕
          </a-button>
          <a-button
            v-if="detail?.status === 'failed' || detail?.status === 'completed'"
            type="primary"
            status="warning"
            @click="handleTriggerCorrection"
          >
            <template #icon><icon-sync /></template>
            重新批改
          </a-button>
          <a-button type="primary" @click="goBack">
            <template #icon><icon-arrow-left /></template>
            返回列表
          </a-button>
        </a-space>
      </template>

      <a-spin style="display: block" :loading="loading">
        <a-descriptions :data="basicInfo" :column="3" size="large" bordered />

        <a-row class="mb-4">
          <a-col :span="8">
            <a-space>
              <!-- 第一列：学生答卷 -->
              <a-card title="学生答卷" :bordered="false">
                <a-tabs v-model:activeKey="studentImageTab">
                  <a-tab-pane key="original" title="原图">
                    <div class="file-item">
                      <a-image v-if="detail?.file_url" :width="150" :src="detail.file_url" />
                      <div v-else class="no-image">暂无学生答卷原图</div>
                    </div>
                  </a-tab-pane>
                  <a-tab-pane key="marked" title="批改图">
                    <div class="file-item">
                      <a-image
                        v-if="detail?.marked_file_url"
                        :width="150"
                        :src="detail.marked_file_url"
                      />
                      <div v-else class="no-image">暂无学生答卷批改图</div>
                    </div>
                  </a-tab-pane>
                </a-tabs>
              </a-card>
            </a-space>
          </a-col>
          <a-col :span="8">
            <a-space>
              <!-- 第二列：标准答案 -->
              <a-card title="标准答案" :bordered="false">
                <a-button
                  type="primary"
                  @click="
                    () => {
                      answerVisible = true
                    }
                  "
                  >查看答案</a-button
                >
              </a-card>
            </a-space>
          </a-col>
          <a-col :span="8">
            <a-space>
              <!-- 第三列：批改详情 -->
              <a-card title="批改详情" :bordered="false">
                <template v-if="detail?.status === 'completed' && detail?.correct_result">
                  <div class="result-container">
                    <a-descriptions :column="1" bordered>
                      <a-descriptions-item label="学号">
                        {{ detail.correct_result.student_number }}
                      </a-descriptions-item>
                      <a-descriptions-item label="总得分">
                        {{ getScoreDisplay(getTotalScore(detail.correct_result.answer)) }}
                      </a-descriptions-item>
                    </a-descriptions>

                    <div class="mt-4">
                      <h3>题目得分情况</h3>
                      <div class="questions-grid">
                        <div
                          v-for="item in detail.correct_result.answer"
                          :key="getQuestionKey(item)"
                          class="question-box"
                          :class="getColorClass(item.right)"
                          @click="showQuestionDetail(item)"
                        >
                          {{ getQuestionDisplay(item) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </template>

                <template v-else-if="detail?.status === 'in_progress'">
                  <a-result
                    status="info"
                    title="批改中"
                    sub-title="系统正在批改中，请稍后刷新页面查看结果"
                  />
                </template>

                <template v-else-if="detail?.status === 'waiting'">
                  <a-result
                    status="warning"
                    title="待批改"
                    sub-title="系统尚未开始批改，请稍后刷新页面查看结果"
                  />
                </template>

                <template v-else-if="detail?.status === 'failed'">
                  <a-result
                    status="error"
                    title="批改失败"
                    sub-title="系统批改失败，请联系管理员"
                  />
                </template>
              </a-card>
            </a-space>
          </a-col>
        </a-row>
        <div class="three-column-layout"></div>

        <!-- 题目详情弹窗 -->
        <a-modal
          v-model:visible="questionDetailVisible"
          :title="`题目 ${getQuestionDisplay(currentQuestion) || ''} 详情`"
          @cancel="closeQuestionDetail"
          :footer="false"
          :width="600"
        >
          <div v-if="currentQuestion" class="question-detail">
            <a-descriptions :column="1" bordered layout="vertical">
              <a-descriptions-item label="得分">
                <a-tag :color="getScoreColor(currentQuestion.right)" size="medium">{{
                  getScoreDisplay(currentQuestion.score)
                }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="学生作答">
                <div class="answer-content">{{ currentQuestion.answer }}</div>
              </a-descriptions-item>
              <a-descriptions-item label="给分原因">
                <div class="reason-content">{{ currentQuestion.reason }}</div>
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </a-modal>
      </a-spin>
    </a-card>
    <a-image-preview-group v-model:visible="answerVisible" :srcList="task?.answer_file_urls" />
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Message, Modal } from '@arco-design/web-vue'
import { IconSync, IconEdit } from '@arco-design/web-vue/es/icon'
import {
  getCorrectionTaskDetail,
  getUploadQueueItemDetail,
  triggerCorrection,
  markItem,
  reParseContent,
  PARSE_STATUS,
} from '@/api/correction'
import type {
  UploadQueueItemDetail,
  CorrectResultItem,
  CorrectionTaskDTO,
  // MarkItemResponse,
} from '@/api/correction'

const route = useRoute()
const router = useRouter()
const itemId = computed(() => route.params.itemId as string)

const loading = ref(false)
const detail = ref<UploadQueueItemDetail | null>(null)
const task = ref<CorrectionTaskDTO | null>(null)
const questionDetailVisible = ref(false)
const currentQuestion = ref<CorrectResultItem | null>(null)
const answerVisible = ref(false)

// 学生答卷图片切换相关状态
const studentImageTab = ref('original')

const basicInfo = computed(() => {
  if (!detail.value) return []

  return [
    {
      label: 'ID',
      value: detail.value.item_id,
    },
    {
      label: '序号',
      value: '' + detail.value.sequence_number,
    },
    {
      label: '状态',
      value: getStatusText(detail.value.status),
    },
  ]
})

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    waiting: '待批改',
    in_progress: '批改中',
    completed: '已批改',
    failed: '批改失败',
  }
  return statusMap[status] || '未知'
}

const getTotalScore = (output: CorrectResultItem[] | undefined) => {
  if (!output) return 0
  return output.reduce((total, item) => total + item.score, 0)
}

// 获取分数显示文本，负数显示为"无评分标准"
const getScoreDisplay = (score: number) => {
  return score >= 0 ? `${score}分` : '无评分标准'
}

const getColorClass = (right: number) => {
  if (right === 1) return 'question-box-green'
  if (right === 0) return 'question-box-red'
  return 'question-box-yellow'
}

const getScoreColor = (right: number) => {
  if (right === 1) return 'green'
  if (right === 0) return 'red'
  return 'orange'
}

// 获取任务详情
const fetchTaskDetail = async () => {
  if (!detail.value) return

  loading.value = true
  try {
    const res = await getCorrectionTaskDetail(detail.value.task_id)
    task.value = res
  } catch (error) {
    Message.error('获取批改任务详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 获取题目的唯一键
const getQuestionKey = (question: CorrectResultItem | null): string => {
  if (!question) return ''
  return `${question.main_question}-${question.sub_question}`
}

// 获取题目的显示文本
const getQuestionDisplay = (question: CorrectResultItem | null): string => {
  if (!question) return ''
  if (question.sub_question) {
    return `${question.main_question}(${question.sub_question})`
  }
  return question.main_question
}

const showQuestionDetail = (question: CorrectResultItem) => {
  currentQuestion.value = question
  questionDetailVisible.value = true
}

const closeQuestionDetail = () => {
  questionDetailVisible.value = false
  currentQuestion.value = null
}

const fetchData = async () => {
  if (!itemId.value) return

  loading.value = true
  try {
    const res = await getUploadQueueItemDetail(itemId.value)
    detail.value = res

    await fetchTaskDetail()
  } catch (error) {
    Message.error('获取批改任务详情失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

const goBack = () => {
  router.back()
}

// 触发原卷留痕
const handleMarkItem = async () => {
  if (!detail.value || !detail.value.item_id) return

  const itemId = detail.value.item_id

  try {
    loading.value = true
    const res = await markItem(itemId)

    // 更新批改图URL
    if (res.marked_file_url) {
      detail.value.marked_file_url = res.marked_file_url
      // 自动切换到批改图标签
      studentImageTab.value = 'marked'
      Message.success('原卷留痕成功')
    } else {
      Message.warning('未获取到留痕文件URL')
    }
  } catch (error) {
    Message.error('原卷留痕失败')
    console.error(error)
  } finally {
    loading.value = false
  }
}

// 触发重新批改
const handleTriggerCorrection = async () => {
  if (!detail.value || !detail.value.item_id) return

  // 获取任务ID
  const taskId = detail.value.task_id
  if (!taskId) {
    Message.error('无法获取任务ID')
    return
  }

  const itemId = detail.value.item_id

  // 先刷新任务详情，获取最新的答案解析状态
  await fetchTaskDetail()

  // 检查答案解析状态
  if (task.value?.answer_parse_status === PARSE_STATUS.IN_PROGRESS) {
    Modal.warning({
      title: '答案解析中',
      content: '答案正在解析中，请稍等2分钟后再试。',
    })
    return
  }

  // 如果答案未解析或解析失败，提示用户并询问是否触发解析
  if (
    !task.value?.answer_parse_status ||
    task.value.answer_parse_status === PARSE_STATUS.WAITING ||
    task.value.answer_parse_status === PARSE_STATUS.FAILED
  ) {
    Modal.confirm({
      title: '答案未解析',
      content: '答案尚未解析，需要先解析答案才能进行批改。是否立即解析答案？',
      onOk: async () => {
        try {
          await reParseContent(taskId, { type: 'answer' })
          Message.success('已触发答案解析，请稍后再试')
        } catch (error) {
          Message.error('触发答案解析失败')
          console.error(error)
        }
      },
    })
    return
  }

  // 答案已解析完成，可以进行批改
  Modal.confirm({
    title: '确认重新批改',
    content: '您确定要重新批改此项目吗？',
    onOk: async () => {
      try {
        await triggerCorrection(taskId, {
          item_ids: [itemId],
        })
        Message.success(`成功触发批改`)
        // 刷新数据
        fetchData()
      } catch (error) {
        Message.error('触发批改失败')
        console.error(error)
      }
    },
  })
}

// 监听 itemId 变化，当切换批改项时重新获取数据
watch(
  itemId,
  (newVal) => {
    if (newVal) {
      // 重置数据
      detail.value = null
      // 获取新的批改项数据
      fetchData()
    }
  },
  { immediate: false },
)

onMounted(() => {
  fetchData()
})
</script>

<style scoped lang="less">
.container {
  padding: 0 20px 20px 20px;
}

.general-card {
  margin-top: 20px;
}

// 三列布局
.three-column-layout {
  display: flex;
  gap: 20px;
  margin-top: 20px;

  @media (max-width: 1200px) {
    flex-wrap: wrap;
  }

  @media (max-width: 768px) {
    flex-direction: column;
  }
}

.column {
  flex: 1;
  min-width: 0;

  @media (max-width: 1200px) and (min-width: 769px) {
    flex-basis: calc(50% - 10px);

    &:last-child {
      flex-basis: 100%;
      margin-top: 20px;
    }
  }
}

.no-image {
  color: #86909c;
  font-size: 16px;
}

.file-actions {
  margin-top: 12px;
  text-align: center;
}

// 批改结果相关样式
.result-container {
  margin-top: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.questions-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 12px;
}

.question-box {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 16px;
  font-weight: bold;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    opacity: 0.8;
    transform: translateY(-2px);
  }
}

.question-box-green {
  background-color: #00b42a;
}

.question-box-yellow {
  background-color: #ff7d00;
}

.question-box-red {
  background-color: #f53f3f;
}

.question-detail {
  padding: 10px;
}

.answer-content,
.reason-content {
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.file-item {
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;
}
</style>
