<template>
  <div class="container">
    <a-card title="租户详情">
      <a-row :gutter="16" class="mb-4">
        <a-col :span="4">
          <a-card class="metric-card">
            <div class="metric-title">剩余点数</div>
            <div class="metric-value">{{ points }}</div>
          </a-card>
        </a-col>
      </a-row>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { getTenantDetail } from '@/api/tenant'
import { useUserStore } from '@/store'
import { storeToRefs } from 'pinia'
import { ref } from 'vue'

const points = ref(0)
const userStore = useUserStore()
const { tenantId } = storeToRefs(userStore)

const loadData = () => {
  getTenantDetail(tenantId!.value!)
    .then((res) => {
      points.value = res.tenantPoint
    })
    .catch((err) => {
      console.error(err)
    })
}

loadData()
</script>

<style scoped>
.container {
  padding: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.metric-card {
  height: 120px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.metric-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 12px;
}

.metric-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 4px;
}
</style>
