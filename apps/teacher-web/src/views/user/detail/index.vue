<template>
  <div class="container">
    <a-row style="margin-bottom: 16px">
      <a-col :span="24">
        <UserCard :data="data" />
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import UserCard from './components/UserCard.vue'
import { getUserDetail, type UserDetailResp } from '@/api/user'
import { ref } from 'vue'
import { useQuery } from '@/hooks/query'

const query = useQuery<{
  id: string
}>()
const data = ref<UserDetailResp>()
const fetchData = async () => {
  const res = await getUserDetail(query.value.id)
  data.value = res
}

void fetchData()
</script>

<script lang="ts">
export default {
  name: 'UserDetail',
}
</script>

<style scoped lang="scss">
.container {
  padding: 20px;
}

.wrapper {
  padding: 20px;
  min-height: 580px;
  background-color: var(--color-bg-2);
  border-radius: 4px;
}
</style>
