import { type AppRouteRecordRaw } from '@/router/routes/types.ts'
import { DEFAULT_LAYOUT } from '@/router/routes/base.ts'

const CORRECTION: AppRouteRecordRaw = {
  path: '/correction',
  name: 'Correction',
  component: DEFAULT_LAYOUT,
  meta: {
    locale: '批改管理',
    icon: 'icon-file',
    order: 0,
  },
  children: [
    {
      path: 'tasks',
      name: 'CorrectionTaskList',
      component: async () => await import('@/views/correction/task-list/index.vue'),
      meta: {
        locale: '任务列表',
      },
    },
    {
      path: 'tasks/:taskId/items',
      name: 'CorrectionItemsList',
      component: async () => await import('@/views/correction/items-list/index.vue'),
      meta: {
        locale: '单页列表',
        hideInMenu: true,
      },
    },
    {
      path: 'tasks/:taskId/scannerList',
      name: 'CorrectionScannerList',
      component: async () => await import('@/views/correction/scanner-list/index.vue'),
      meta: {
        locale: '扫描仪列表',
        hideInMenu: true,
      },
    },
    {
      path: 'tasks/:taskId/sheets',
      name: 'CorrectionSheetsList',
      component: async () => await import('@/views/correction/sheets-list/index.vue'),
      meta: {
        locale: '答题卡列表',
        hideInMenu: true,
      },
    },
    {
      path: 'sheets/:sheetId',
      name: 'CorrectionSheetDetail',
      component: async () => await import('@/views/correction/sheet-detail/index.vue'),
      meta: {
        locale: '答题卡详情',
        hideInMenu: true,
      },
    },
    {
      path: 'items/:itemId',
      name: 'CorrectionItemDetail',
      component: async () => await import('@/views/correction/item-detail/index.vue'),
      meta: {
        locale: '单页详情',
        hideInMenu: true,
      },
    },
    {
      path: 'tasks/:taskId',
      name: 'CorrectionTaskDetail',
      component: async () => await import('@/views/correction/task-detail/index.vue'),
      meta: {
        locale: '任务详情',
        hideInMenu: true,
      },
    },
  ],
}

export default CORRECTION
