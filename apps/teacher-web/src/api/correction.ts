import request from '@/utils/request'
import { PageResponse } from './type'

interface DashboardRequest {
  startTime: number
  endTime: number
}

export interface TaskRunStatus {
  taskId: string
  taskName: string
  count: number
  userCount: number
}

export interface DashboardResponse {
  expiredUserCount: number
  effectiveUserCount: number
  totalUserCount: number
  newUserCount: number
  taskRunStatus: TaskRunStatus[]
}

export function getDashboardData(data: DashboardRequest) {
  return request.get<DashboardResponse>('/api/v1/correction/dashboard', data)
}

interface UserListRequest {
  page: number
  size: number
}

export interface UserResponse {
  uuid: string
  wxNickName: string
  wxAvatarUrl: string
  createdAt: string
  vipTtl: string
}

export function getEffectiveUsers(req: UserListRequest) {
  return request.get<PageResponse<UserResponse>>('/api/v1/correction/users/effective', req)
}

export function getExpiredUsers(req: UserListRequest) {
  return request.get<PageResponse<UserResponse>>('/api/v1/correction/users/expired', req)
}

interface TaskListRequest {
  page: number
  size: number
}

export interface TaskResponse {
  runId: string
  taskId: string
  name: string
  taskName: string
  status: number
  createdAt: string
}

export function getTaskList(req: TaskListRequest) {
  return request.get<PageResponse<TaskResponse>>('/api/v1/correction/taskRuns', req)
}

// 批改任务列表请求参数
export interface ListCorrectionItemsRequest {
  page: number
  size: number
}

// 切题状态
export const SegmentationStatus = {
  NONE: '', // 未切题
  MANUAL: 'manual', // 手动切题
  APPLIED: 'applied', // 自动应用切题
}

// 上传队列项（批改任务项）
export interface UploadQueueItem {
  item_id: string
  task_id: string
  file_url: string
  student_number: string
  sequence_number: number
  status: string
  answer_file_url?: string // 答案图片URL，只在详情页返回
  marked_file_url?: string // 批改后的图片URL
}

// 批改结果项
export interface CorrectResultItem {
  main_question: string // 大题题号
  sub_question: string // 小题题号
  score: number // 单题得分
  reason: string // 给分原因，常用于主观题
  right: number // 0-1 的数，代表当前学生作答与答案的匹配程度
  answer: string // 学生作答内容

  top_left?: Location // 答题区域左上角坐标
  bottom_right?: Location // 答题区域右下角坐标
  item_id?: string // 所属上传队列项ID
}

// 批改结果
export interface CorrectResult {
  student_number: string
  answer: CorrectResultItem[]
}

// 批改任务详情
export interface UploadQueueItemDetail extends UploadQueueItem {
  correct_result: CorrectResult | null
}

// 获取当前任务下的全部已上传任务
export function listCorrectionItems(taskId: string, params: ListCorrectionItemsRequest) {
  return request.get<PageResponse<UploadQueueItem>>(`/api/v1/corrections/${taskId}/items`, params)
}

// 获取批改任务详情
export function getUploadQueueItemDetail(itemId: string) {
  return request.get<UploadQueueItemDetail>(`/api/v1/correct-items/${itemId}`)
}

// 解析状态枚举
export const PARSE_STATUS = {
  WAITING: 'waiting', // 待解析
  IN_PROGRESS: 'in_progress', // 解析中
  COMPLETED: 'completed', // 已解析
  FAILED: 'failed', // 解析失败
}

// 批改任务DTO
export interface CorrectionTaskDTO {
  id: string // 任务ID
  paper_double_sided?: boolean // 试卷是否双面打印
  paper_vertical_arrangement?: boolean // 试卷是否垂直排列
  answer_double_sided?: boolean // 答案是否双面打印
  answer_vertical_arrangement?: boolean // 答案是否垂直排列
  title: string // 任务标题
  status: string // 任务状态
  need_scan_paper: boolean // 是否需要扫描试卷题目
  paper_with_answer: boolean // 答题卡和试卷题目是否在一起
  sheets_per_paper: number // 每张答题卡有多少张纸
  double_sided: boolean // 是否双面打印
  vertical_arrangement: boolean // 是否为竖向放入
  paper_file_ids?: string[] // 试卷文件ID列表 (已弃用)
  answer_file_ids?: string[] // 答案文件ID列表 (已弃用)
  paper_file_urls: string[] // 试卷文件URL列表
  answer_file_urls: string[] // 答案文件URL列表
  total_sheets: number // 总答题卡数量
  corrected_sheets: number // 已批改答题卡数量
  in_progress_sheets: number // 正在批改中的答题卡数量
  failed_sheets: number // 批改失败的答题卡数量
  grouping_status: string // 分组状态 ("" | "in_progress" | "completed")
  paper_parse_status: string // 试卷解析状态
  answer_parse_status: string // 答案解析状态
  created_at: string // 创建时间
  updated_at: string // 更新时间
}

// 批改任务列表请求参数
export interface ListCorrectionTasksRequest {
  page: number
  size: number
}

// 获取批改任务列表
export function listCorrectionTasks(params: ListCorrectionTasksRequest) {
  return request.get<PageResponse<CorrectionTaskDTO>>('/api/v1/corrections', params)
}

// 创建批改任务请求参数
export interface CreateCorrectionTaskRequest {
  title: string // 任务标题
  paper_double_sided?: boolean // 试卷是否双面打印
  paper_vertical_arrangement?: boolean // 试卷是否垂直排列
  answer_double_sided?: boolean // 答案是否双面打印
  answer_vertical_arrangement?: boolean // 答案是否垂直排列
  need_scan_paper: boolean // 是否需要扫描试卷题目
  paper_with_answer: boolean // 答题卡和试卷题目是否在一起
  auto_correction: boolean // 是否自动批改
  sheets_per_paper: number // 每张答题卡有多少张纸
  double_sided: boolean // 是否双面打印
  vertical_arrangement: boolean // 是否为竖向放入
}

// 创建批改任务响应
export interface CreateCorrectionTaskResponse {
  id: string // 新创建的任务ID
}

// 创建批改任务
export function createCorrectionTask(data: CreateCorrectionTaskRequest) {
  return request.post<CreateCorrectionTaskResponse>('/api/v1/corrections', data)
}

// 获取批改任务详情
export function getCorrectionTaskDetail(taskId: string) {
  return request.get<CorrectionTaskDTO>(`/api/v1/corrections/${taskId}`)
}

// 批改项目分组请求
export interface GroupCorrectionItemsRequest {
  has_student_number: boolean // 是否每张答题卡都有学号信息
  force_by_sheets: boolean // 是否强制按纸张数量分组
}

// 批改项目分组
export function groupCorrectionItems(taskId: string, data: GroupCorrectionItemsRequest) {
  return request.post(`/api/v1/corrections/${taskId}/group`, data)
}

// 试卷信息
export interface SheetDTO {
  id: string // 试卷ID
  task_id: string // 所属任务ID
  student_number: string // 学号
  item_count: number // 包含的上传队列项数量
  created_at: string // 创建时间
  status: string // 任务状态
  segmentation_status?: string // 切题状态
}

export interface SheetDetail extends SheetDTO {
  correct_result: CorrectResult | null
}

// 获取试卷列表请求
export interface GetSheetsRequest {
  page: number
  size: number
}

// 获取试卷列表
export function getSheets(taskId: string, params: GetSheetsRequest) {
  return request.get<PageResponse<SheetDTO>>(`/api/v1/corrections/${taskId}/sheets`, params)
}

// 获取试卷详情
export function getSheetDetail(sheetId: string) {
  return request.get<SheetDetail>(`/api/v1/sheets/${sheetId}`)
}

// 获取试卷的上传队列项
export function getSheetItems(sheetId: string, params: ListCorrectionItemsRequest) {
  return request.get<PageResponse<UploadQueueItem>>(`/api/v1/sheets/${sheetId}/items`, params)
}

// 单个项目的切题结果
export interface ItemSegmentation {
  main_question: string // 大题题号
  sub_question: string // 小题题号
  top_left: Location // 答题区域左上角坐标
  bottom_right: Location // 答题区域右下角坐标
}

// 坐标位置
export interface Location {
  x: number
  y: number
}

// 保存切题结果请求
export interface SaveSegmentationRequest {
  segmentations: ItemSegmentation[] // 切题结果列表
}

// 保存切题结果
export function saveSheetSegmentation(
  sheetId: string,
  itemId: string,
  data: SaveSegmentationRequest,
) {
  return request.post(`/api/v1/sheets/${sheetId}/segmentation/${itemId}`, data)
}

// 应用切题结果请求
export interface ApplySegmentationRequest {
  reference_sheet_id: string // 参考试卷ID
}

// 应用切题结果
export function applySheetSegmentation(taskId: string, data: ApplySegmentationRequest) {
  return request.post(`/api/v1/segmentation/${taskId}/apply`, data)
}

export interface ListPrinterResponse {
  printerId: string
  expiredAt: string
  status: string
  scannedAt: string
  bindAt: string
}

export function listTaskPrinters(taskId: string) {
  return request.get<ListPrinterResponse[]>(`/api/v1/corrections/${taskId}/printers`)
}

export function deleteTaskPrinter(taskId: string, printerId: string) {
  return request.delete(`/api/v1/corrections/${taskId}/printers/${printerId}`)
}

// 触发批改请求
export interface TriggerCorrectionRequest {
  item_ids?: string[] // 需要批改的队列项ID列表
  sheet_ids?: string[] // 需要批改的试卷ID列表
}

// 触发批改响应
export interface TriggerCorrectionResponse {
  count: number // 触发批改的数量
}

// 触发批改
export function triggerCorrection(taskId: string, data: TriggerCorrectionRequest) {
  return request.post<TriggerCorrectionResponse>(`/api/v1/corrections/${taskId}/trigger`, data)
}

// 获取原卷留痕PDF响应
export interface GetMarkedPDFResponse {
  url: string // PDF文件URL
}

// 获取整个任务的留痕PDF
export function getMarkedPDF(taskId: string) {
  return request.get<GetMarkedPDFResponse>(`/api/v1/corrections/${taskId}/marked-pdf`)
}

// 对批改完成的任务进行原卷留痕响应
export interface MarkTaskResponse {
  marked_count: number // 成功标记的数量
}

// 对批改完成的任务进行原卷留痕
export function markTask(taskId: string) {
  return request.post<MarkTaskResponse>(`/api/v1/corrections/${taskId}/mark`)
}

// 对单个批改项进行原卷留痕响应
export interface MarkItemResponse {
  marked_file_url: string // 批改后的文件URL
}

// 对单个批改项进行原卷留痕
export function markItem(itemId: string) {
  return request.post<MarkItemResponse>(`/api/v1/correct-items/${itemId}/mark`)
}

// 对整个答题卡进行原卷留痕响应
export interface MarkSheetResponse {
  marked_count: number // 成功标记的数量
}

// 对整个答题卡进行原卷留痕
export function markSheet(sheetId: string) {
  return request.post<MarkSheetResponse>(`/api/v1/sheets/${sheetId}/mark`)
}

// 获取答题卡留痕PDF响应
export interface GetSheetMarkedPDFResponse {
  url: string // PDF文件URL
}

// 获取单个答题卡的留痕PDF
export function getSheetMarkedPDF(sheetId: string) {
  return request.get<GetSheetMarkedPDFResponse>(`/api/v1/sheets/${sheetId}/marked-pdf`)
}

// 重新解析请求参数
export interface ReParseContentRequest {
  type: string // 解析类型：paper-试卷，answer-答案，空-全部
}

// 重新解析试卷或答案
export function reParseContent(taskId: string, data: ReParseContentRequest) {
  return request.post(`/api/v1/corrections/${taskId}/reparse`, data)
}

// 更新任务设置请求参数
export interface UpdateTaskSettingsRequest {
  need_scan_paper?: boolean // 是否需要扫描试卷题目
  paper_double_sided?: boolean // 试卷是否双面打印
  paper_vertical_arrangement?: boolean // 试卷是否垂直排列
  answer_double_sided?: boolean // 答案是否双面打印
  answer_vertical_arrangement?: boolean // 答案是否垂直排列
  paper_with_answer?: boolean // 答题卡和试卷题目是否在一起
  auto_correction?: boolean // 是否自动批改
  sheets_per_paper?: number // 每张答题卡有多少张纸
  double_sided?: boolean // 是否双面打印
  vertical_arrangement?: boolean // 是否竖向放入
  title?: string // 任务标题
}

// 更新任务设置
export function updateTaskSettings(taskId: string, data: UpdateTaskSettingsRequest) {
  return request.put(`/api/v1/corrections/${taskId}/settings`, data)
}
