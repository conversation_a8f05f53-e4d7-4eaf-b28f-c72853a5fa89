import request from '@/utils/request'

export interface GetCurrentUserResponse {
  userId: number
  username: string
  nickname: string
  avatar: string
}

export async function getUserInfo() {
  return await request.get<GetCurrentUserResponse>('/api/v1/auth/current')
}

interface updatePasswordReq {
  newPassword: string
  oldPassword: string
  repeatPassword: string
}

export async function updatePassword(data: updatePasswordReq) {
  return await request.post('/api/v1/my/password', data)
}

export interface GetMyTenantsResponse {
  roleCode: string
  roleId: string
  roleName: string
  tenantId: string
  tenantName: string
}

export function getMyTenants() {
  return request.get<GetMyTenantsResponse[]>('/api/v1/my/tenants')
}
