import request from '@/utils/request'

export interface GetTenantDetailResponse {
  tenantId: string
  tenantName: string
  tenantPoint: number
}

export function getTenantDetail(tenantId: string) {
  return request.get<GetTenantDetailResponse>(`/api/v1/tenants/detail`, {
    tenantId: tenantId,
  })
}

interface JoinTenantRequest {
  tenantCode: string
}

export function joinTenant(data: JoinTenantRequest) {
  return request.post('/api/v1/tenants/join', data)
}
