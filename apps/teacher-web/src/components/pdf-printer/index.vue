<template>
  <a-button
    type="primary"
    :status="buttonStatus"
    :loading="loading"
    @click="handlePrint"
    v-bind="$attrs"
  >
    <template #icon>
      <icon-printer />
    </template>
    {{ buttonText }}
  </a-button>
  <a-modal
    :visible="printModelVisible"
    @ok="
      () => {
        printPDF(pdfUrl)
        printModelVisible = false
      }
    "
    @cancel="printModelVisible = false"
    :closable="false"
    title="打印确认"
  >
    <div>
      确认要直接打印吗？您也可以点击此<a-link target="_blank" :href="pdfUrl">链接</a-link>下载PDF文件并手动打印
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import { IconPrinter } from '@arco-design/web-vue/es/icon'

declare global {
  interface Window {
    pdfjsLib: any
  }
}

interface Props {
  buttonText?: string
  buttonStatus?: 'normal' | 'warning' | 'success' | 'danger'
  disabled?: boolean
  loadUrl?: () => Promise<string | undefined>
}

const props = withDefaults(defineProps<Props>(), {
  buttonText: '打印PDF',
  buttonStatus: 'normal',
  disabled: false,
})

const loading = ref(false)
const pdfJsLoaded = ref(false)
const pdfUrl = ref('')
const printModelVisible = ref(false)

// 加载PDF.js库
const loadPdfJsIfNeeded = async () => {
  if (pdfJsLoaded.value) return

  return new Promise<void>((resolve, reject) => {
    try {
      if (window.pdfjsLib) {
        pdfJsLoaded.value = true
        resolve()
        return
      }

      const script = document.createElement('script')
      script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js'
      script.crossOrigin = 'anonymous'
      script.async = true

      script.onload = () => {
        window.pdfjsLib.GlobalWorkerOptions.workerSrc =
          'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
        pdfJsLoaded.value = true
        resolve()
      }

      script.onerror = () => {
        reject(new Error('加载PDF.js库失败'))
      }

      document.head.appendChild(script)
    } catch (error) {
      reject(error)
    }
  })
}

const printPDF = async (pdfUrl: string) => {
  try {
    await loadPdfJsIfNeeded()

    const loadingTask = window.pdfjsLib.getDocument({
      url: pdfUrl,
      withCredentials: true,
    })

    const pdf = await loadingTask.promise
    const numPages = pdf.numPages

    // 创建一个隐藏的iframe专门用于打印
    const iframe = document.createElement('iframe')
    iframe.style.position = 'fixed'
    iframe.style.right = '0'
    iframe.style.bottom = '0'
    iframe.style.width = '0'
    iframe.style.height = '0'
    iframe.style.border = '0'
    document.body.appendChild(iframe)

    // 等待iframe加载完成
    await new Promise((resolve) => {
      iframe.onload = resolve
      iframe.srcdoc = `<!DOCTYPE html><html><head><title>打印预览</title></head><body></body></html>`
    })

    const iframeDoc = iframe.contentDocument!
    const iframeWindow = iframe.contentWindow!

    // 添加打印样式
    const style = iframeDoc.createElement('style')
    style.textContent = `
      @page {
        size: auto;
        margin: 0;
      }
      body {
        margin: 0;
        padding: 0;
      }
      .pdf-page {
        page-break-after: always;
        width: 100%;
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .pdf-page:last-child {
        page-break-after: auto;
      }
      canvas {
        max-width: 100%;
        max-height: 100%;
      }
    `
    iframeDoc.head.appendChild(style)

    // 渲染所有PDF页面
    for (let i = 1; i <= numPages; i++) {
      const page = await pdf.getPage(i)
      const viewport = page.getViewport({ scale: 1.5 })

      const pageDiv = iframeDoc.createElement('div')
      pageDiv.className = 'pdf-page'

      const canvas = iframeDoc.createElement('canvas')
      canvas.width = viewport.width
      canvas.height = viewport.height

      pageDiv.appendChild(canvas)
      iframeDoc.body.appendChild(pageDiv)

      await page.render({
        canvasContext: canvas.getContext('2d')!,
        viewport: viewport,
      }).promise
    }

    // 打印iframe内容
    setTimeout(() => {
      iframeWindow.focus()
      iframeWindow.print()

      // 打印完成后移除iframe
      setTimeout(() => {
        document.body.removeChild(iframe)
        loading.value = false
      }, 1000)
    }, 500)
  } catch (error) {
    console.error('PDF打印失败:', error)
    Message.error(`PDF打印失败: ${error instanceof Error ? error.message : '未知错误'}`)
    loading.value = false

    Modal.info({
      title: '无法自动打印PDF',
      content: '您可以尝试直接下载PDF文件并手动打印',
      okText: '下载PDF',
      onOk: () => {
        window.open(pdfUrl, '_blank')
      },
    })
  }
}

// 处理打印按钮点击
const handlePrint = async () => {
  if (!props.loadUrl) {
    Message.error('PDF URL为空，无法打印')
    return
  }

  if (props.disabled) {
    return
  }

  loading.value = true
  try {
    const url = await props.loadUrl()
    if (!url) {
      Message.error('PDF URL为空，无法打印')
      loading.value = false
      return
    }
    pdfUrl.value = url
    printModelVisible.value = true
    loading.value = false
  } catch (error) {
    Message.error('打印PDF失败')
    console.error(error)
    loading.value = false
  }
}
</script>
