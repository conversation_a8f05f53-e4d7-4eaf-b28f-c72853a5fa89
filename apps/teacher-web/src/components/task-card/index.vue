<template>
  <div class="task-card" @click="viewTaskDetail">
    <!-- Card Header with Title and Class -->
    <div class="task-card-header">
      <div class="task-title">{{ task.title }}</div>
      <div class="task-actions">
        <a-dropdown trigger="hover" @select="handleMenuSelect">
          <a-button type="text" size="mini">
            <icon-more />
          </a-button>
          <template #content>
            <!-- <a-doption value="view">查看详情</a-doption> -->
            <a-doption value="sheets">查看答题卡</a-doption>
            <a-doption value="scanners">查看关联扫描仪</a-doption>
          </template>
        </a-dropdown>
      </div>
    </div>

    <!-- Class Tag -->
    <!-- <div class="task-info">
      <a-tag size="small" color="orange">示例班级</a-tag>
      <a-tag size="small" color="gray">总分: {{ totalScore }}分</a-tag>
    </div> -->

    <!-- Teacher Name -->
    <!-- <div class="task-teacher">{{ teacherName }}</div> -->

    <!-- Status Counts -->
    <div class="task-status-counts">
      <div class="status-item">
        <div class="status-label">已扫描</div>
        <div class="status-value">{{ task.total_sheets }}</div>
      </div>
      <div class="status-item">
        <div class="status-label">已批改</div>
        <div class="status-value">{{ task.corrected_sheets }}</div>
      </div>
      <div class="status-item">
        <div class="status-label">批改中</div>
        <div class="status-value">{{ task.in_progress_sheets }}</div>
      </div>
      <div class="status-item">
        <div class="status-label">失败</div>
        <div class="status-value">{{ task.failed_sheets }}</div>
      </div>
    </div>

    <!-- Timestamp -->
    <div class="task-timestamp">
      {{ formatDate(task.created_at) }}
      <a-link class="view-details" @click.stop="viewTaskDetail"> 点击查看 <icon-right /> </a-link>
    </div>
  </div>
</template>

<script lang="ts" setup>
// import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { IconMore, IconRight } from '@arco-design/web-vue/es/icon'
import type { CorrectionTaskDTO } from '@/api/correction'

const props = defineProps<{
  task: CorrectionTaskDTO
  teacherName?: string
  totalScore?: number
}>()

const router = useRouter()

// Default values for optional props
// const teacherName = computed(() => props.teacherName || '默认教师')
// const totalScore = computed(() => props.totalScore || 30)

// Format date to a readable string
const formatDate = (date: string) => {
  if (!date) return ''
  const d = new Date(date)
  return d.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  })
}

// Navigate to task detail page
const viewTaskDetail = () => {
  router.push({
    name: 'CorrectionTaskDetail',
    params: { taskId: props.task.id },
  })
}

// Handle dropdown menu selection
const handleMenuSelect = (value: string | number | Record<string, any> | undefined, _: Event) => {
  const key = String(value)
  switch (key) {
    case 'view':
      viewTaskDetail()
      break
    case 'sheets':
      router.push({
        name: 'CorrectionSheetsList',
        params: { taskId: props.task.id },
      })
      break
    case 'scanners':
      router.push({
        name: 'CorrectionScannerList',
        params: { taskId: props.task.id },
      })
      break
    default:
      Message.info(`选择了 ${key}`)
  }
}
</script>

<style scoped lang="less">
.task-card {
  position: relative;
  border: 1px solid rgb(var(--color-neutral-3));
  border-radius: 16px;
  padding: 16px;
  margin-bottom: 16px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  .task-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 8px;

    .task-title {
      font-size: 18px;
      font-weight: bold;
      color: rgb(var(--color-text-1));
      flex: 1;
    }

    .task-actions {
      flex-shrink: 0;
    }
  }

  .task-info {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
  }

  .task-teacher {
    font-size: 14px;
    color: rgb(var(--color-text-3));
    margin-bottom: 16px;
  }

  .task-status-counts {
    display: flex;
    background-color: rgb(var(--color-fill-2));
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 16px;

    .status-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;

      .status-label {
        font-size: 14px;
        color: rgb(var(--color-text-3));
        margin-bottom: 4px;
      }

      .status-value {
        font-size: 18px;
        font-weight: bold;
        color: rgb(var(--color-text-1));
      }
    }
  }

  .task-timestamp {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: rgb(var(--color-text-3));

    .view-details {
      display: flex;
      align-items: center;
      font-size: 14px;
    }
  }
}
</style>
