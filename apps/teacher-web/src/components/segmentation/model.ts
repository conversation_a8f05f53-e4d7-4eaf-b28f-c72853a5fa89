// Define segmentation types

// Segmentation status constants
export const SegmentationStatus = {
  NONE: '', // 未切题
  MANUAL: 'manual', // 手动切题
  APPLIED: 'applied', // 自动应用切题
}

// Location interface
export interface Location {
  x: number
  y: number
}

// Item segmentation interface
export interface ItemSegmentation {
  main_question: string // 大题题号
  sub_question: string // 小题题号
  top_left: Location // 答题区域左上角坐标（相对值）
  bottom_right: Location // 答题区域右下角坐标（相对值）

  // 兼容旧版本
  number?: string // 旧版题号，格式为：大题(小题)，例如1(2)表示第1大题的第2小题
}

// Save segmentation request interface
export interface SaveSegmentationRequest {
  segmentations: ItemSegmentation[] // 切题结果列表
}

// Apply segmentation request interface
export interface ApplySegmentationRequest {
  reference_sheet_id: string // 参考试卷ID
}
