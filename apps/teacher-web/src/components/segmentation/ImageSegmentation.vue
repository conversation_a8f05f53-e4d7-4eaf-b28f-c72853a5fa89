<template>
  <div class="image-segmentation">
    <div class="image-controls">
      <div class="zoom-controls">
        <a-button-group>
          <a-button @click="zoomIn">
            <template #icon><icon-zoom-in /></template>
            放大
          </a-button>
          <a-button @click="zoomOut">
            <template #icon><icon-zoom-out /></template>
            缩小
          </a-button>
          <a-button @click="resetZoom">
            <template #icon><icon-refresh /></template>
            重置
          </a-button>
        </a-button-group>
        <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
      </div>
    </div>

    <div
      class="image-wrapper"
      ref="imageWrapper"
    >
      <div
        class="image-container"
        :style="{ transform: `scale(${zoomLevel})` }"
      >
        <div
          class="drawing-area"
          ref="drawingArea"
        >
          <img
            :src="props.imageUrl"
            ref="imageElement"
            @load="onImageLoad"
            class="target-image"
            draggable="false"
          />

          <!-- 当前正在绘制的矩形 -->
          <div
            v-if="isDrawing"
            class="selection-rect current"
            :style="currentRectStyle"
          ></div>

          <!-- 已保存的矩形，按题号分组显示 -->
          <div
            v-for="(rect, index) in segmentations"
            :key="index"
            class="selection-rect saved"
            :class="{ highlighted: getQuestionKey(rect) === selectedQuestion }"
            :style="getSavedRectStyle(rect)"
          >
            <div class="rect-label">{{ getQuestionDisplay(rect) }}</div>
          </div>
        </div>
      </div>
    </div>

    <div class="controls">
      <div
        class="selected-question"
        v-if="selectedQuestion"
      >
        <p class="question-info">
          当前选中题号：<strong>{{ selectedQuestion }}</strong>
        </p>
        <p class="instruction">请在图片上按住鼠标左键并拖动来框选该题的答题区域</p>
        <p
          v-if="currentQuestionSegmentations.length > 0"
          class="existing-info"
        >
          当前题目已有 {{ currentQuestionSegmentations.length }} 个标记区域
        </p>
      </div>
      <div
        class="no-question"
        v-else
      >
        <p class="instruction">请先选择一个题号，然后在图片上框选答题区域</p>
      </div>
      <div class="action-buttons">
        <a-button
          type="primary"
          @click="addSegmentation"
          :disabled="!isValidSegmentation || !selectedQuestion"
        >
          添加切题区域
        </a-button>
        <a-button
          type="text"
          @click="clearCurrentSelection"
          class="ml-2"
        > 清除当前选择 </a-button>
      </div>
    </div>

    <div
      class="segmentation-list"
      v-if="segmentations.length > 0"
    >
      <h3>已添加的切题区域</h3>
      <div class="question-filter">
        <a-radio-group
          v-model="segmentationFilter"
          type="button"
        >
          <a-radio value="all">所有题目</a-radio>
          <a-radio
            v-if="selectedQuestion"
            :value="selectedQuestion"
          >当前题目 ({{ selectedQuestion }})</a-radio>
        </a-radio-group>
      </div>
      <a-table
        :data="filteredSegmentations"
        :pagination="false"
      >
        <template #columns>
          <a-table-column
            title="题号"
            data-index="number"
          >
            <template #cell="{ record }">
              <span :class="{ 'highlighted-text': getQuestionKey(record) === selectedQuestion }">
                {{ getQuestionDisplay(record) }}
              </span>
            </template>
          </a-table-column>
          <a-table-column
            title="左上角坐标"
            :width="120"
          >
            <template #cell="{ record }">
              ({{ record.top_left.x }}, {{ record.top_left.y }})
            </template>
          </a-table-column>
          <a-table-column
            title="右下角坐标"
            :width="120"
          >
            <template #cell="{ record }">
              ({{ record.bottom_right.x }}, {{ record.bottom_right.y }})
            </template>
          </a-table-column>
          <a-table-column
            title="操作"
            :width="100"
          >
            <template #cell="{ record }">
              <a-button
                type="text"
                status="danger"
                @click="removeSegmentation(record)"
              >
                删除
              </a-button>
            </template>
          </a-table-column>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { IconZoomIn, IconZoomOut, IconRefresh } from '@arco-design/web-vue/es/icon'
import type { ItemSegmentation, Location } from './model'

// 定义组件属性
interface Props {
  imageUrl: string
  // initialSegmentations?: ItemSegmentation[]
  selectedQuestion?: string
}

const props = withDefaults(defineProps<Props>(), {
  // initialSegmentations: () => [],
  selectedQuestion: '',
})

// 定义组件事件
// const emit = defineEmits(['update:segmentations'])

const segmentations = defineModel<ItemSegmentation[]>('segmentations', {
  default: () => [],
})

// 组件状态
const imageElement = ref<HTMLImageElement | null>(null)
const imageWrapper = ref<HTMLElement | null>(null)
const drawingArea = ref<HTMLElement | null>(null)
const isDrawing = ref(false)
const segmentationFilter = ref<string>('all') // 切题区域筛选：all 或 特定题号

// 缩放相关状态
const zoomLevel = ref(1)
const MIN_ZOOM = 0.5
const MAX_ZOOM = 3
const ZOOM_STEP = 0.1

// 当前选择区域
const selectionStart = reactive({ x: 0, y: 0 })
const selectionEnd = reactive({ x: 0, y: 0 })
const currentSegmentation = reactive<{
  main_question: string
  sub_question: string
  top_left: Location
  bottom_right: Location
}>({
  main_question: '',
  sub_question: '',
  top_left: { x: 0, y: 0 },
  bottom_right: { x: 0, y: 0 },
})

// 计算属性：当前选择是否有效
const isValidSegmentation = computed(() => {
  return (
    props.selectedQuestion &&
    currentSegmentation.top_left.x !== currentSegmentation.bottom_right.x &&
    currentSegmentation.top_left.y !== currentSegmentation.bottom_right.y
  )
})

// 获取题目的唯一键
const getQuestionKey = (question: ItemSegmentation): string => {
  return `${question.main_question}-${question.sub_question}`
}

// 获取题目的显示文本
const getQuestionDisplay = (question: ItemSegmentation): string => {
  if (question.sub_question) {
    return `${question.main_question}(${question.sub_question})`
  }
  return question.main_question
}

// 解析选中的题号
const parseSelectedQuestion = (): { main: string; sub: string } => {
  if (!props.selectedQuestion) return { main: '', sub: '' }

  const parts = props.selectedQuestion.split('-')
  if (parts.length === 2) {
    return { main: parts[0], sub: parts[1] }
  }
  return { main: props.selectedQuestion, sub: '' }
}

// 计算属性：按题号分组的切题区域
const segmentationsByQuestion = computed(() => {
  const result: Record<string, ItemSegmentation[]> = {}

  segmentations.value.forEach((seg) => {
    const key = getQuestionKey(seg)
    if (!result[key]) {
      result[key] = []
    }
    result[key].push(seg)
  })

  return result
})

// 当前题目已有的切题区域
const currentQuestionSegmentations = computed(() => {
  return segmentationsByQuestion.value[props.selectedQuestion || ''] || []
})

// 过滤后的切题区域列表（用于表格显示）
const filteredSegmentations = computed(() => {
  if (segmentationFilter.value === 'all') {
    return segmentations.value
  } else {
    return segmentations.value.filter((seg) => getQuestionKey(seg) === segmentationFilter.value)
  }
})

// 计算属性：当前矩形的样式
const currentRectStyle = computed(() => {
  if (!isDrawing.value) return {}

  const left = Math.min(selectionStart.x, selectionEnd.x)
  const top = Math.min(selectionStart.y, selectionEnd.y)
  const width = Math.abs(selectionEnd.x - selectionStart.x)
  const height = Math.abs(selectionEnd.y - selectionStart.y)

  return {
    left: `${left}px`,
    top: `${top}px`,
    width: `${width}px`,
    height: `${height}px`,
  }
})

// 获取已保存矩形的样式
const getSavedRectStyle = (rect: ItemSegmentation) => {
  if (!imageElement.value) return {}

  // 将相对坐标转换为当前显示尺寸下的像素坐标
  const currentWidth = imageElement.value.width
  const currentHeight = imageElement.value.height

  const left = rect.top_left.x * currentWidth
  const top = rect.top_left.y * currentHeight
  const width = (rect.bottom_right.x - rect.top_left.x) * currentWidth
  const height = (rect.bottom_right.y - rect.top_left.y) * currentHeight

  return {
    left: `${left}px`,
    top: `${top}px`,
    width: `${width}px`,
    height: `${height}px`,
  }
}

// 监听选中题号变化
watch(
  () => props.selectedQuestion,
  (newVal) => {
    if (newVal) {
      // 更新当前选择的题号
      const { main, sub } = parseSelectedQuestion()
      currentSegmentation.main_question = main
      currentSegmentation.sub_question = sub
    }
  },
)

// 添加图片原始尺寸记录
const imageNaturalWidth = ref(0)
const imageNaturalHeight = ref(0)

// 图片加载完成后的处理
const onImageLoad = () => {
  if (imageElement.value) {
    // 记录图片原始尺寸
    imageNaturalWidth.value = imageElement.value.naturalWidth
    imageNaturalHeight.value = imageElement.value.naturalHeight
  }
  setupDrawingEvents()
}

// 设置绘图区域的事件监听
const setupDrawingEvents = () => {
  if (!drawingArea.value) return

  // 阻止默认拖动行为
  drawingArea.value.addEventListener('dragstart', (e) => e.preventDefault())

  // 鼠标按下开始绘制
  drawingArea.value.addEventListener('mousedown', (e) => {
    if (!props.selectedQuestion) return

    const rect = drawingArea.value!.getBoundingClientRect()
    selectionStart.x = e.clientX - rect.left
    selectionStart.y = e.clientY - rect.top
    selectionEnd.x = selectionStart.x
    selectionEnd.y = selectionStart.y
    isDrawing.value = true
  })

  // 鼠标移动绘制矩形
  window.addEventListener('mousemove', (e) => {
    if (!isDrawing.value || !drawingArea.value) return

    const rect = drawingArea.value.getBoundingClientRect()
    selectionEnd.x = e.clientX - rect.left
    selectionEnd.y = e.clientY - rect.top
  })

  // 鼠标松开完成绘制
  window.addEventListener('mouseup', () => {
    if (!isDrawing.value) return

    // 更新当前选择的矩形坐标
    currentSegmentation.top_left = {
      x: Math.min(selectionStart.x, selectionEnd.x),
      y: Math.min(selectionStart.y, selectionEnd.y),
    }
    currentSegmentation.bottom_right = {
      x: Math.max(selectionStart.x, selectionEnd.x),
      y: Math.max(selectionStart.y, selectionEnd.y),
    }

    isDrawing.value = false

    // 如果选择有效，直接添加切题区域
    const width = Math.abs(selectionEnd.x - selectionStart.x)
    const height = Math.abs(selectionEnd.y - selectionStart.y)

    // 确保矩形足够大才添加（避免意外点击）
    if (props.selectedQuestion && width > 10 && height > 10) {
      addSegmentation()
    }
  })
}

// 缩放相关方法
const zoomIn = () => {
  if (zoomLevel.value < MAX_ZOOM) {
    zoomLevel.value = Math.min(MAX_ZOOM, zoomLevel.value + ZOOM_STEP)
  }
}

const zoomOut = () => {
  if (zoomLevel.value > MIN_ZOOM) {
    zoomLevel.value = Math.max(MIN_ZOOM, zoomLevel.value - ZOOM_STEP)
  }
}

const resetZoom = () => {
  zoomLevel.value = 1
}

// 添加或更新切题区域
const addSegmentation = () => {
  if (!isValidSegmentation.value || !imageElement.value) return

  // 计算相对坐标（相对于原始图片尺寸的百分比）
  const relativeTopLeft = {
    x: currentSegmentation.top_left.x / (imageElement.value.width / zoomLevel.value),
    y: currentSegmentation.top_left.y / (imageElement.value.height / zoomLevel.value),
  }

  const relativeBottomRight = {
    x: currentSegmentation.bottom_right.x / (imageElement.value.width / zoomLevel.value),
    y: currentSegmentation.bottom_right.y / (imageElement.value.height / zoomLevel.value),
  }

  // 查找当前题号是否已有切题区域
  const currentKey = `${currentSegmentation.main_question}-${currentSegmentation.sub_question}`
  const existingIndex = segmentations.value.findIndex((seg) => getQuestionKey(seg) === currentKey)

  const newSegmentation = {
    main_question: currentSegmentation.main_question,
    sub_question: currentSegmentation.sub_question,
    top_left: relativeTopLeft,
    bottom_right: relativeBottomRight,
    // 保存原始图片尺寸信息
    original_width: imageNaturalWidth.value,
    original_height: imageNaturalHeight.value,
    // 兼容旧版本
    number: getQuestionDisplay(currentSegmentation),
  }

  if (existingIndex >= 0) {
    // 如果已存在，更新该区域
    segmentations.value[existingIndex] = newSegmentation
  } else {
    // 如果不存在，创建新区域
    segmentations.value.push(newSegmentation)
  }

  // 清除当前选择
  clearCurrentSelection()
}

// 清除当前选择
const clearCurrentSelection = () => {
  selectionStart.x = 0
  selectionStart.y = 0
  selectionEnd.x = 0
  selectionEnd.y = 0
  currentSegmentation.top_left = { x: 0, y: 0 }
  currentSegmentation.bottom_right = { x: 0, y: 0 }
}

// 删除切题区域
const removeSegmentation = (segmentToRemove: ItemSegmentation) => {
  // 查找要删除的切题区域在原始数组中的索引
  const indexToRemove = segmentations.value.findIndex(
    (seg) =>
      seg.main_question === segmentToRemove.main_question &&
      seg.sub_question === segmentToRemove.sub_question &&
      seg.top_left.x === segmentToRemove.top_left.x &&
      seg.top_left.y === segmentToRemove.top_left.y &&
      seg.bottom_right.x === segmentToRemove.bottom_right.x &&
      seg.bottom_right.y === segmentToRemove.bottom_right.y,
  )

  if (indexToRemove !== -1) {
    segmentations.value.splice(indexToRemove, 1)
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    // 处理窗口大小变化逻辑
  })
})
</script>

<style scoped lang="less">
.image-segmentation {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .image-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .zoom-controls {
      display: flex;
      align-items: center;
      gap: 12px;

      .zoom-level {
        font-size: 14px;
        color: #4e5969;
        min-width: 50px;
      }
    }
  }

  .image-wrapper {
    width: 100%;
    border: 1px solid #e5e6eb;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    height: auto; // 固定高度，可以根据需要调整

    .image-container {
      transform-origin: top left;
      transition: transform 0.2s ease;
      position: relative;
      display: inline-block;

      .drawing-area {
        position: relative;
        display: inline-block;

        .target-image {
          display: block;
          max-width: 100%;
          user-select: none;
          -webkit-user-drag: none;
          pointer-events: none;
        }

        .selection-rect {
          position: absolute;
          box-sizing: border-box;
          pointer-events: none;

          &.current {
            background-color: rgba(255, 0, 0, 0.3);
            border: 2px solid rgba(255, 0, 0, 0.8);
          }

          &.saved {
            background-color: rgba(0, 255, 0, 0.3);
            border: 2px solid rgba(0, 128, 0, 0.8);
            transition: all 0.2s ease;

            &.highlighted {
              background-color: rgba(46, 149, 245, 0.4);
              border: 2px solid rgba(46, 149, 245, 0.9);
              box-shadow: 0 0 5px rgba(46, 149, 245, 0.5);
              z-index: 10;
            }

            .rect-label {
              position: absolute;
              top: -20px;
              left: 0;
              background-color: rgba(0, 128, 0, 0.8);
              color: white;
              padding: 2px 6px;
              font-size: 12px;
              border-radius: 2px;
              white-space: nowrap;
            }

            &.highlighted .rect-label {
              background-color: rgba(46, 149, 245, 0.9);
              font-weight: bold;
            }
          }
        }
      }
    }
  }

  .controls {
    margin-top: 16px;

    .selected-question {
      margin-bottom: 16px;
      padding: 12px;
      background-color: #f2f3f5;
      border-radius: 4px;

      .question-info {
        font-size: 16px;
        margin-bottom: 8px;
      }

      .instruction {
        color: #86909c;
        margin-bottom: 0;
      }
    }

    .no-question {
      margin-bottom: 16px;
      padding: 12px;
      background-color: #fff3f0;
      border-radius: 4px;

      .instruction {
        color: #f53f3f;
        margin-bottom: 0;
      }
    }

    .action-buttons {
      display: flex;
      margin-bottom: 16px;
    }
  }

  .segmentation-list {
    margin-top: 16px;

    h3 {
      margin-bottom: 12px;
      font-size: 16px;
      font-weight: 500;
    }
  }

  .ml-2 {
    margin-left: 8px;
  }
}
</style>
