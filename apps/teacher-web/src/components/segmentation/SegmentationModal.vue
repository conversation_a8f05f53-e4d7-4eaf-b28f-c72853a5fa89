<template>
  <a-modal
    v-model:visible="visible"
    title="答题区域切题"
    :width="900"
    :footer="false"
    @cancel="handleCancel"
  >
    <a-spin :loading="loading">
      <div class="segmentation-modal">
        <div class="description">
          <p>请选择题号，然后在图片上框选对应的答题区域。</p>
        </div>

        <div class="question-selection">
          <a-form
            layout="horizontal"
            :model="{ selectedQuestion: selectedQuestionNumber }"
          >
            <a-form-item
              field="selectedQuestion"
              label="选择题号"
            >
              <a-select
                v-model="selectedQuestionNumber"
                placeholder="请选择题号"
                :loading="questionsLoading"
                :disabled="questionsLoading || !questions.length"
              >
                <a-option
                  v-for="question in questions"
                  :key="getQuestionKey(question)"
                  :value="getQuestionKey(question)"
                >
                  {{ getQuestionDisplay(question) }}
                </a-option>
              </a-select>
            </a-form-item>
          </a-form>
        </div>

        <a-tabs v-model:activeKey="activeTab">
          <a-tab-pane
            key="student"
            title="学生答卷"
          >
            <div
              v-if="studentImageUrl"
              class="image-container"
            >
              <ImageSegmentation
                :image-url="studentImageUrl"
                :selected-question="selectedQuestionNumber"
                v-model:segmentations="segmentations"
              />
            </div>
            <div
              v-else
              class="no-image"
            >
              <a-empty description="暂无学生答卷图片" />
            </div>
          </a-tab-pane>
        </a-tabs>

        <div class="actions">
          <a-space>
            <a-button @click="handleCancel">取消</a-button>
            <a-button
              type="primary"
              @click="handleSave"
              :loading="saving"
            >保存切题结果</a-button>
          </a-space>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue'
import { Message } from '@arco-design/web-vue'
import ImageSegmentation from './ImageSegmentation.vue'
import {
  saveSheetSegmentation,
  type CorrectResultItem,
  type ItemSegmentation,
  getSheetDetail,
} from '@/api/correction'

// 定义组件属性
interface Props {
  visible: boolean
  sheetId: string
  itemId: string
  answerImageUrl?: string
  studentImageUrl?: string
}

const props = withDefaults(defineProps<Props>(), {
  answerImageUrl: '',
  studentImageUrl: '',
})

// 定义组件事件
const emit = defineEmits(['update:visible', 'saved'])

// 组件状态
const loading = ref(false)
const saving = ref(false)
const activeTab = ref('student')
const segmentations = ref<ItemSegmentation[]>([])

// 题目相关状态
const questions = ref<CorrectResultItem[]>([])
const questionsLoading = ref(false)
const selectedQuestionNumber = ref('')

// 计算属性：弹窗可见性
const visible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val),
})

// 监听弹窗可见性变化
watch(
  () => props.visible,
  (newVal) => {
    console.log('SegmentationModal - visible changed:', newVal)
    if (newVal) {
      // 当弹窗打开时，重置状态
      activeTab.value = 'student'
      selectedQuestionNumber.value = ''

      // 获取题目列表和切题结果
      fetchSheetDetails()
    }
  },
)

// 监听选中题号变化
watch(
  () => selectedQuestionNumber.value,
  (newVal) => {
    if (newVal) {
      console.log('Selected question number:', newVal)
    }
  },
)

// 获取题目的唯一键
const getQuestionKey = (question: CorrectResultItem): string => {
  return `${question.main_question}-${question.sub_question}`
}

// 获取题目的显示文本
const getQuestionDisplay = (question: CorrectResultItem): string => {
  if (question.sub_question) {
    return `${question.main_question}(${question.sub_question})`
  }
  return question.main_question
}

// 获取项目详情（包含题目列表和切题结果）
const fetchSheetDetails = async () => {
  if (!props.itemId) return

  loading.value = true
  questionsLoading.value = true

  try {
    const detail = await getSheetDetail(props.sheetId)

    // 处理题目列表
    if (detail.correct_result && detail.correct_result.answer) {
      questions.value = detail.correct_result.answer

      // 处理切题结果数据
      // 将CorrectResultItem转换为ItemSegmentation格式
      const segmentationData: ItemSegmentation[] = []

      detail.correct_result.answer.forEach((item) => {
        // 只添加有坐标信息的题目
        if (item.top_left && item.bottom_right && item.item_id === props.itemId) {
          segmentationData.push({
            main_question: item.main_question,
            sub_question: item.sub_question,
            top_left: item.top_left,
            bottom_right: item.bottom_right,
          })
        }
      })

      console.log('segmentationData', segmentationData)
      if (segmentationData.length > 0) {
        segmentations.value = segmentationData
      } else {
        // 如果没有切题数据，则清空
        segmentations.value = []
      }
    } else {
      questions.value = []
      segmentations.value = []
      Message.warning('没有找到题目信息')
    }
  } catch (error) {
    console.error('获取项目信息失败:', error)
    Message.error('获取项目信息失败')
    questions.value = []
    segmentations.value = []
  } finally {
    loading.value = false
    questionsLoading.value = false
  }
}

// // 更新切题区域
// const updateSegmentations = (newSegmentations: ItemSegmentation[]) => {
//   segmentations.value = newSegmentations
// }

// 保存切题结果
const handleSave = async () => {
  if (segmentations.value.length === 0) {
    Message.warning('请至少添加一个切题区域')
    return
  }

  saving.value = true
  try {
    // 保存切题结果
    await saveSheetSegmentation(props.sheetId, props.itemId, {
      segmentations: segmentations.value,
    })

    Message.success('保存切题结果成功')
    emit('saved')
    emit('update:visible', false)
  } catch (error) {
    console.error('保存切题结果失败:', error)
    Message.error('保存切题结果失败')
  } finally {
    saving.value = false
  }
}

// 取消
const handleCancel = () => {
  emit('update:visible', false)
}
</script>

<style scoped lang="less">
.segmentation-modal {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .description {
    margin-bottom: 16px;
    color: #86909c;
  }

  .question-selection {
    margin-bottom: 20px;
    padding: 16px;
    background-color: #f7f8fa;
    border-radius: 4px;
  }

  .image-container {
    width: 100%;
    margin-bottom: 16px;
    max-height: 600px;
    overflow: auto;
  }

  .no-image {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 300px;
    background-color: #f2f3f5;
    border-radius: 4px;
  }

  .preview-image {
    max-width: 100%;
    max-height: 500px;
    display: block;
    margin: 0 auto;
  }

  .actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
  }
}
</style>
