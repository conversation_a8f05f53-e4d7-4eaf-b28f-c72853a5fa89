# /opt/bin/nginx -c /code/nginx.conf -g 'daemon off;'

events {
  worker_connections 1024;
}
http {
  server {
    error_log /dev/stderr;
    access_log /dev/stdout;

    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 2;
    gzip_types text/html text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png image/svg+xml;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";
    include /opt/nginx/conf/mime.types;
    add_header Access-Control-Allow-Origin *;

    listen 80;

    location / {
      root /code/dist;
      index index.html;
      add_header Cache-Control "max-age=0";
      try_files $uri $uri/ /index.html =404;
    }

    location ~ ^/.(images|javascript|js|css|flash|media|static)/ {
      root /code/dist;
    }

  }
}