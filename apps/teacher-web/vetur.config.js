module.exports = {
  // vetur configuration for the teacher-web project
  projects: [
    {
      // project root directory
      root: '.',
      // configure Vetur to use the tsconfig.json in the project root
      tsconfig: './tsconfig.json',
      // configure Vetur to use the jsconfig.json in the project root (if it exists)
      jsconfig: './jsconfig.json',
      // configure Vetur to use the package.json in the project root
      package: './package.json',
      // configure Vetur to use the vite.config.ts in the project root
      viteConfig: './vite.config.ts',
      // configure Vetur to use the global components in the project
      globalComponents: [
        './src/components/**/*.vue'
      ]
    }
  ]
}
