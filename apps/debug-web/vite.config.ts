import { defineConfig, loadEnv, type UserConfig } from 'vite'
import { join } from 'path'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import vueDevTools from 'vite-plugin-vue-devtools'
import { ArcoResolver } from 'unplugin-vue-components/resolvers'

// https://vitejs.dev/config/
export default ({ mode }: any): UserConfig => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd()) }
  let baseURL = "http://localhost:18080"

  return defineConfig({
    plugins: [
      vue(),
      vueDevTools(),
      AutoImport({
        resolvers: [ArcoResolver()],
      }),
      Components({
        resolvers: [ArcoResolver()],
      }),
    ],
    server: {
      proxy: {
        '/api/v1': {
          target: baseURL,
          changeOrigin: true,
        },
        '/printer/api/v1': {
          target: baseURL,
          changeOrigin: true,
        },
      },
    },
    resolve: {
      alias: {
        '@': join(__dirname, 'src'),
      },
    },
  })
}
