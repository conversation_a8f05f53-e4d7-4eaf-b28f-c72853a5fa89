{"name": "debug-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@arco-design/web-vue": "^2.56.3", "@vueuse/core": "^12.5.0", "axios": "^1.7.9", "pinia": "^2.3.1", "qrcode": "^1.5.4", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@types/node": "^22.10.8", "@types/qrcode": "^1.5.5", "@vitejs/plugin-vue": "^5.2.1", "less": "^4.3.0", "npm-run-all2": "^7.0.2", "sass": "^1.83.4", "typescript": "^5.7.3", "unplugin-auto-import": "^19.0.0", "unplugin-vue-components": "^28.0.0", "vite": "^6.2.1", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.0"}}