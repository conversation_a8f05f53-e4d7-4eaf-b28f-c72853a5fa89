# Debug Web App

A Vue 3 application for debugging and testing the printer API functionality.

## Features

- QR code authentication for obtaining tokens
- Upload papers and reference answers
- Create upload queues and submit queue items
- Integration with OSS for file uploads
- Global token management for API authentication

## Project Structure

```
├── src
│   ├── api              // API client functions
│   ├── assets           // Static assets
│   │   └── style        // Global styles
│   ├── components       // Reusable components
│   │   └── layout       // Layout components
│   ├── router           // Vue Router configuration
│   ├── store            // Pinia stores
│   ├── utils            // Utility functions
│   ├── views            // Application views
│   │   ├── home         // Home page
│   │   ├── qrcode       // QR code authentication
│   │   ├── upload-papers // Upload papers and answers
│   │   └── queue-upload // Queue-based upload
│   ├── App.vue          // Root component
│   └── main.ts          // Entry point
├── .env                 // Environment variables
├── index.html           // HTML template
├── package.json         // Dependencies
├── tsconfig.json        // TypeScript configuration
└── vite.config.ts       // Vite configuration
```

## API Endpoints

The app interacts with the following API endpoints:

- `GET /printer/api/v1/qrcode` - Generate a QR code for authentication
- `GET /printer/api/v1/qrcode/info` - Get QR code status information
- `POST /printer/api/v1/papers` - Update papers and answers
- `POST /printer/api/v1/upload-queue` - Create an upload queue
- `POST /printer/api/v1/upload-queue/item` - Submit a queue item
- `GET /printer/api/v1/oss/token` - Get OSS upload token

## Development

```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Build for production
pnpm build
```

## Environment Variables

- `VITE_API_URL` - Base URL for API requests (default: http://localhost:8080)
