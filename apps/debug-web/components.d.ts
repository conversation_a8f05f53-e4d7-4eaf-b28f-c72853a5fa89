/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AAlert: typeof import('@arco-design/web-vue')['Alert']
    AButton: typeof import('@arco-design/web-vue')['Button']
    ACard: typeof import('@arco-design/web-vue')['Card']
    ACardMeta: typeof import('@arco-design/web-vue')['CardMeta']
    ACollapse: typeof import('@arco-design/web-vue')['Collapse']
    ACollapseItem: typeof import('@arco-design/web-vue')['CollapseItem']
    AConfigProvider: typeof import('@arco-design/web-vue')['ConfigProvider']
    AForm: typeof import('@arco-design/web-vue')['Form']
    AFormItem: typeof import('@arco-design/web-vue')['FormItem']
    AInput: typeof import('@arco-design/web-vue')['Input']
    AInputNumber: typeof import('@arco-design/web-vue')['InputNumber']
    AModal: typeof import('@arco-design/web-vue')['Modal']
    APageHeader: typeof import('@arco-design/web-vue')['PageHeader']
    AppLayout: typeof import('./src/components/layout/AppLayout.vue')['default']
    ASpace: typeof import('@arco-design/web-vue')['Space']
    ATable: typeof import('@arco-design/web-vue')['Table']
    ATableColumn: typeof import('@arco-design/web-vue')['TableColumn']
    ATag: typeof import('@arco-design/web-vue')['Tag']
    AUpload: typeof import('@arco-design/web-vue')['Upload']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
