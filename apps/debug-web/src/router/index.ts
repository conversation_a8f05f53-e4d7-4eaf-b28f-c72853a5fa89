import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('@/views/home/<USER>'),
    },
    {
      path: '/upload-papers',
      name: 'upload-papers',
      component: () => import('@/views/upload-papers/index.vue'),
    },
    {
      path: '/upload-answers',
      name: 'upload-answers',
      component: () => import('@/views/upload-answers/index.vue'),
    },
    {
      path: '/queue-upload',
      name: 'queue-upload',
      component: () => import('@/views/queue-upload/index.vue'),
    },
    {
      path: '/qrcode',
      name: 'qrcode',
      component: () => import('@/views/qrcode/index.vue'),
    },
  ],
})

export default router
