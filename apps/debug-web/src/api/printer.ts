import request from '@/utils/request'

// UpdatePaper request and response types
export interface UpdatePaperRequest {
  paper_file_ids: string[]
  answer_file_ids: string[]
}

export async function updatePaper(data: UpdatePaperRequest) {
  return await request.post('/printer/api/v1/papers', data)
}

// CreateUploadQueue request and response types
export interface CreateUploadQueueRequest {
  total: number
}

export interface CreateUploadQueueResponse {
  queue_id: string
}

export async function createUploadQueue(data: CreateUploadQueueRequest): Promise<CreateUploadQueueResponse> {
  return await request.post('/printer/api/v1/upload-queue', data)
}

// SubmitQueueItem request and response types
export interface SubmitQueueItemRequest {
  queue_id: string
  file_id: string
  sequence: number
}

export async function submitQueueItem(data: SubmitQueueItemRequest) {
  return await request.post('/printer/api/v1/upload-queue/item', data)
}

// GetOssToken request and response types
export interface GetOssTokenResponse {
  access_key_id: string
  policy: string
  signature: string
  host: string
  object_key: string
  access_url: string
}

export async function getOssToken(ext: string): Promise<GetOssTokenResponse> {
  return await request.get(`/printer/api/v1/oss/token?ext=${ext}`)
}
