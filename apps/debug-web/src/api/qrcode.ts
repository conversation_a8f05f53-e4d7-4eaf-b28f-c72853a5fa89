import request from '@/utils/request'

// QR Code response types
export interface GetQRCodeResponse {
  qr_code_id: string
  content: string
  expire_at: number
}

export interface QRCodeInfoResponse {
  token?: string
  status?: string
  expire_at?: number
}

/**
 * Generate a new QR code
 * @returns QR code data
 */
export async function generateQRCode(): Promise<GetQRCodeResponse> {
  return await request.get('/printer/api/v1/qrcode')
}

/**
 * Get information about a QR code
 * @param qrCodeId QR code ID
 * @returns QR code information
 */
export async function getQRCodeInfo(qrCodeId: string): Promise<QRCodeInfoResponse> {
  return await request.get(`/printer/api/v1/qrcode/info?qr_code_id=${qrCodeId}`)
}
