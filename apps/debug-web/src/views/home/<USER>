<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useTokenStore } from '@/store/token'

const router = useRouter()
const tokenStore = useTokenStore()

const navigateTo = (path: string) => {
  router.push(path)
}
</script>

<template>
  <div class="container">
    <div class="home-container">
      <h1 class="text-center">调试工具</h1>
      <p class="text-center">用于测试和调试批改系统的工具</p>

      <a-alert v-if="!tokenStore.token" type="warning" class="mt-20 mb-20">
        <template #message>
          请先设置项目 Token 以进行 API 认证
        </template>
      </a-alert>



      <div class="card-container mt-20">
        <div class="menu-grid">
          <a-card hoverable @click="navigateTo('/qrcode')">
            <template #cover>
              <div class="card-icon">
                <icon-qrcode size="32" />
              </div>
            </template>
            <a-card-meta title="二维码认证">
              <template #description>扫描二维码获取 Token</template>
            </a-card-meta>
          </a-card>

          <a-card hoverable @click="navigateTo('/upload-papers')">
            <template #cover>
              <div class="card-icon">
                <icon-file-pdf size="32" />
              </div>
            </template>
            <a-card-meta title="上传试卷和答案">
              <template #description>手动上传试卷和参考答案</template>
            </a-card-meta>
          </a-card>

          <a-card hoverable @click="navigateTo('/queue-upload')">
            <template #cover>
              <div class="card-icon">
                <icon-upload size="32" />
              </div>
            </template>
            <a-card-meta title="队列上传">
              <template #description>创建上传队列并提交图片</template>
            </a-card-meta>
          </a-card>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.home-container {
  padding: 40px 0;

  h1 {
    font-size: 28px;
    margin-bottom: 10px;
  }

  p {
    color: var(--color-text-3);
    margin-bottom: 30px;
  }
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.card-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 120px;
  background-color: var(--color-fill-2);
  color: var(--color-primary);
}
</style>
