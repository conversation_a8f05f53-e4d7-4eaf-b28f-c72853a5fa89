<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import { uploadFileToOss } from '@/utils/upload'
import { updatePaper } from '@/api/printer'
import { useTokenStore } from '@/store/token'

// Get token store
const tokenStore = useTokenStore()

// Define interface for file items with uid
interface FileItem {
  uid: string;
  file?: File; // Make file optional to match Arco Design's FileItem interface
  name?: string;
}

// State for file uploads
const paperFiles = ref<FileItem[]>([])
const answerFiles = ref<FileItem[]>([])
const uploadingPapers = ref(false)
const uploadingAnswers = ref(false)
const submitting = ref(false)

// Uploaded file IDs
const paperFileIds = reactive<string[]>([])
const answerFileIds = reactive<string[]>([])

// Handle paper file selection
const handlePaperFilesChange = (fileList: FileItem[], fileItem: FileItem) => {
  if (fileList) {
    paperFiles.value = fileList
    return
  }
  if (fileItem) {
    paperFiles.value = [fileItem]
    return
  }
}

// Handle answer file selection
const handleAnswerFilesChange = (fileList: FileItem[], fileItem: FileItem) => {
  if (fileList) {
    answerFiles.value = fileList
    return
  }
  if (fileItem) {
    answerFiles.value = [fileItem]
    return
  }
}

// Upload paper files to OSS
const uploadPaperFiles = async () => {
  if (paperFiles.value.length === 0) {
    Message.warning('请选择试卷文件')
    return
  }

  uploadingPapers.value = true
  paperFileIds.length = 0 // Clear previous IDs

  try {
    for (const fileItem of paperFiles.value) {
      if (!fileItem.file) {
        console.error('文件不存在', fileItem)
        continue
      }
      const fileId = await uploadFileToOss(fileItem.file)
      paperFileIds.push(fileId)
    }
    Message.success(`成功上传 ${paperFileIds.length} 个试卷文件`)
  } catch (error) {
    console.error('上传试卷文件失败:', error)
    Message.error('上传试卷文件失败')
  } finally {
    uploadingPapers.value = false
  }
}

// Upload answer files to OSS
const uploadAnswerFiles = async () => {
  if (answerFiles.value.length === 0) {
    Message.warning('请选择答案文件')
    return
  }

  uploadingAnswers.value = true
  answerFileIds.length = 0 // Clear previous IDs

  try {
    for (const fileItem of answerFiles.value) {
      if (!fileItem.file) {
        console.error('文件不存在', fileItem)
        continue
      }
      const fileId = await uploadFileToOss(fileItem.file)
      answerFileIds.push(fileId)
    }
    Message.success(`成功上传 ${answerFileIds.length} 个答案文件`)
  } catch (error) {
    console.error('上传答案文件失败:', error)
    Message.error('上传答案文件失败')
  } finally {
    uploadingAnswers.value = false
  }
}

// Submit papers and answers
const submitPapersAndAnswers = async () => {
  if (paperFileIds.length === 0 && answerFileIds.length === 0) {
    Message.warning('请先上传试卷或答案文件')
    return
  }

  submitting.value = true

  try {
    await updatePaper({
      paper_file_ids: paperFileIds,
      answer_file_ids: answerFileIds
    })
    Message.success('成功提交试卷和答案')

    // Reset state
    paperFiles.value = []
    answerFiles.value = []
    paperFileIds.length = 0
    answerFileIds.length = 0
  } catch (error) {
    console.error('提交试卷和答案失败:', error)
    Message.error('提交试卷和答案失败')
  } finally {
    submitting.value = false
  }
}
</script>

<template>
  <div class="container">
    <div class="page-container">
      <a-page-header
        title="上传试卷和答案"
        @back="() => $router.push('/')"
      />

      <a-alert
        v-if="!tokenStore.token"
        type="warning"
        class="mb-20"
      >
        <template #message>
          请先设置项目 Token 以进行 API 认证
        </template>
      </a-alert>

      <div class="card-container">
        <a-form
          :model="{}"
          layout="vertical"
        >
          <!-- Paper Files Section -->
          <a-form-item label="试卷文件">
            <div class="upload-section">
              <a-upload
                action="/"
                :auto-upload="false"
                :file-list="paperFiles"
                :show-file-list="true"
                multiple
                @change="handlePaperFilesChange"
              >
                <template #upload-button>
                  <a-button type="primary">
                    <template #icon>
                      <icon-plus />
                    </template>
                    选择试卷文件
                  </a-button>
                </template>
              </a-upload>

              <a-button
                type="primary"
                status="success"
                :loading="uploadingPapers"
                :disabled="paperFiles.length === 0"
                @click="uploadPaperFiles"
              >
                上传试卷文件
              </a-button>
            </div>

            <div
              v-if="paperFileIds.length > 0"
              class="uploaded-files mt-10"
            >
              <a-tag
                v-for="(id, index) in paperFileIds"
                :key="index"
                color="blue"
              >
                {{ id }}
              </a-tag>
            </div>
          </a-form-item>

          <!-- Answer Files Section -->
          <a-form-item label="答案文件">
            <div class="upload-section">
              <a-upload
                action="/"
                :auto-upload="false"
                :file-list="answerFiles"
                :show-file-list="true"
                multiple
                @change="handleAnswerFilesChange"
              >
                <template #upload-button>
                  <a-button type="primary">
                    <template #icon>
                      <icon-plus />
                    </template>
                    选择答案文件
                  </a-button>
                </template>
              </a-upload>

              <a-button
                type="primary"
                status="success"
                :loading="uploadingAnswers"
                :disabled="answerFiles.length === 0"
                @click="uploadAnswerFiles"
              >
                上传答案文件
              </a-button>
            </div>

            <div
              v-if="answerFileIds.length > 0"
              class="uploaded-files mt-10"
            >
              <a-tag
                v-for="(id, index) in answerFileIds"
                :key="index"
                color="green"
              >
                {{ id }}
              </a-tag>
            </div>
          </a-form-item>

          <!-- Submit Button -->
          <a-form-item>
            <a-button
              type="primary"
              long
              :loading="submitting"
              :disabled="paperFileIds.length === 0 && answerFileIds.length === 0"
              @click="submitPapersAndAnswers"
            >
              提交试卷和答案
            </a-button>
          </a-form-item>
        </a-form>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.upload-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.uploaded-files {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
</style>
