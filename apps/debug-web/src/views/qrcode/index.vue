<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { useQRCodeStore, QR_CODE_STATUS } from '@/store/qrcode'
import QRCode from 'qrcode'

const qrcodeStore = useQRCodeStore()

// State
const isGenerating = ref(false)
const qrCodeImageUrl = ref('')
const timerInterval = ref<number | null>(null)
const localRemainingTime = ref('')

// Computed
const statusText = computed(() => {
  switch (qrcodeStore.qrCodeStatus) {
    case QR_CODE_STATUS.SCANNED:
      return '已扫描，等待绑定项目'
    case QR_CODE_STATUS.BIND:
      return '已绑定项目'
    default:
      return '等待扫描'
  }
})

const statusColor = computed(() => {
  switch (qrcodeStore.qrCodeStatus) {
    case QR_CODE_STATUS.SCANNED:
      return 'orange'
    case QR_CODE_STATUS.BIND:
      return 'green'
    default:
      return 'blue'
  }
})

// Methods
const generateQRCode = async () => {
  if (isGenerating.value) return

  isGenerating.value = true
  try {
    await qrcodeStore.generateNewQRCode()
    if (qrcodeStore.qrCodeData) {
      await generateQRCodeImage(qrcodeStore.qrCodeData.content)
      // Restart the timer when a new QR code is generated
      startTimer()
    }
  } catch (error) {
    console.error('Failed to generate QR code:', error)
  } finally {
    isGenerating.value = false
  }
}

const generateQRCodeImage = async (content: string) => {
  try {
    // Generate QR code as data URL
    qrCodeImageUrl.value = await QRCode.toDataURL(content, {
      width: 200,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#ffffff'
      }
    })
  } catch (error) {
    console.error('Failed to generate QR code image:', error)
  }
}

// Watch for QR code data changes
watch(() => qrcodeStore.qrCodeData, async (newData) => {
  if (newData) {
    await generateQRCodeImage(newData.content)
  }
})

// Timer functions
const startTimer = () => {
  // Clear any existing timer
  stopTimer()

  // Update the local remaining time immediately
  updateRemainingTime()

  // Start a new timer that updates every second
  timerInterval.value = window.setInterval(() => {
    updateRemainingTime()

    // Stop the timer if the QR code is expired
    if (qrcodeStore.isExpired) {
      stopTimer()
    }
  }, 1000)
}

const updateRemainingTime = () => {
  if (qrcodeStore.qrCodeData) {
    // Get the remaining time from the store
    const seconds = qrcodeStore.remainingTime
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    localRemainingTime.value = `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }
}

const stopTimer = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value)
    timerInterval.value = null
  }
}

// Lifecycle hooks
onMounted(async () => {
  await generateQRCode()
  startTimer()
})

onUnmounted(() => {
  qrcodeStore.onUnmount()
  stopTimer()
})
</script>

<template>
  <div class="container">
    <a-page-header title="二维码认证" @back="$router.back()" />

    <div class="qrcode-container">
      <a-card :bordered="false" class="qrcode-card">
        <template #title>
          <div class="card-title">
            <span>扫描二维码获取 Token</span>
            <a-tag :color="statusColor">{{ statusText }}</a-tag>
          </div>
        </template>

        <div class="qrcode-content">
          <!-- QR Code Display -->
          <div class="qrcode-image" v-if="qrcodeStore.qrCodeData && qrCodeImageUrl">
            <img
              :src="qrCodeImageUrl"
              alt="QR Code"
              :class="{ expired: qrcodeStore.isExpired }"
            />

            <!-- Expired Overlay -->
            <div class="expired-overlay" v-if="qrcodeStore.isExpired">
              <span>已过期</span>
            </div>
          </div>

          <div class="qrcode-placeholder" v-else>
            <icon-loading v-if="isGenerating" />
            <icon-exclamation-circle v-else />
            <span>{{ isGenerating ? '生成中...' : '无二维码' }}</span>
          </div>

          <!-- Timer -->
          <div class="qrcode-timer" v-if="qrcodeStore.qrCodeData && !qrcodeStore.isExpired">
            <span>有效时间: {{ localRemainingTime }}</span>
          </div>

          <!-- QR Code Content (for debugging) -->
          <div class="qrcode-debug-content" v-if="qrcodeStore.qrCodeData">
            <a-collapse>
              <a-collapse-item header="二维码内容 (调试用)">
                <a target="_blank" :href="qrcodeStore.qrCodeData.content">{{ qrcodeStore.qrCodeData.content }}</a>
              </a-collapse-item>
            </a-collapse>
          </div>

          <!-- Status -->
          <div class="qrcode-status" v-if="qrcodeStore.qrCodeStatus === QR_CODE_STATUS.BIND">
            <a-alert type="success">
              <template #message>
                Token 已保存，可以在页面顶部查看
              </template>
            </a-alert>
          </div>

          <div class="qrcode-status" v-else-if="qrcodeStore.qrCodeStatus === QR_CODE_STATUS.SCANNED">
            <a-alert type="warning">
              <template #message>
                二维码已扫描，等待绑定项目
              </template>
            </a-alert>
          </div>

          <!-- Error -->
          <div class="qrcode-error" v-if="qrcodeStore.qrCodeError">
            <a-alert type="error">
              <template #message>
                {{ qrcodeStore.qrCodeError }}
              </template>
            </a-alert>
          </div>

          <!-- Actions -->
          <div class="qrcode-actions">
            <a-button
              type="primary"
              @click="generateQRCode"
              :loading="isGenerating"
              :disabled="qrcodeStore.qrCodeStatus === QR_CODE_STATUS.BIND"
            >
              {{ qrcodeStore.qrCodeData && !qrcodeStore.isExpired ? '刷新二维码' : '生成二维码' }}
            </a-button>
          </div>
        </div>
      </a-card>

      <!-- Instructions -->
      <a-card :bordered="false" class="instructions-card">
        <template #title>使用说明</template>

        <ol class="instructions-list">
          <li>点击"生成二维码"按钮生成新的二维码</li>
          <li>使用扫描设备扫描二维码</li>
          <li>在扫描设备上选择要绑定的项目</li>
          <li>绑定成功后，Token 将自动保存</li>
          <li>如果二维码过期，请点击"刷新二维码"按钮</li>
        </ol>
      </a-card>
    </div>
  </div>
</template>

<style scoped lang="scss">
.qrcode-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.qrcode-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.qrcode-image {
  position: relative;
  width: 200px;
  height: 200px;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;

    &.expired {
      opacity: 0.5;
      filter: grayscale(1);
    }
  }
}

.expired-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 24px;
  font-weight: bold;
}

.qrcode-placeholder {
  width: 200px;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--color-fill-2);
  color: var(--color-text-3);
  gap: 10px;

  .icon {
    font-size: 48px;
  }
}

.qrcode-timer {
  font-size: 16px;
  color: var(--color-text-2);
}

.qrcode-status, .qrcode-error, .qrcode-debug-content {
  width: 100%;
}

.qrcode-debug-content {
  pre {
    white-space: pre-wrap;
    word-break: break-all;
    background-color: var(--color-fill-1);
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
  }
}

.qrcode-actions {
  margin-top: 20px;
}

.instructions-list {
  padding-left: 20px;

  li {
    margin-bottom: 10px;
    color: var(--color-text-2);
  }
}
</style>
