<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { uploadFileToOss } from '@/utils/upload'
import { createUploadQueue, submitQueueItem } from '@/api/printer'
import { useTokenStore } from '@/store/token'

// Get token store
const tokenStore = useTokenStore()

// State for queue creation
const queueForm = reactive({
  total: 1
})
const queueId = ref('')
const creatingQueue = ref(false)

// State for file uploads
const currentFile = ref<File | null>(null)
const fileList = computed(() => {
  if (!currentFile.value) return []
  return [{
    uid: '1',
    name: currentFile.value.name,
    file: currentFile.value
  }]
})
const currentSequence = ref(1)
const uploadingFile = ref(false)
const submittingItem = ref(false)

// Define interface for uploaded items
interface UploadedItem {
  sequence: number;
  fileId: string;
}

// Uploaded items
const uploadedItems = reactive<Array<UploadedItem>>([])

// Create upload queue
const createQueue = async () => {
  if (queueForm.total <= 0) {
    Message.warning('请输入有效的图片数量')
    return
  }

  creatingQueue.value = true

  try {
    const response = await createUploadQueue({
      total: queueForm.total
    })
    queueId.value = response.queue_id
    Message.success(`成功创建上传队列，ID: ${queueId.value}`)
  } catch (error) {
    console.error('创建上传队列失败:', error)
    Message.error('创建上传队列失败')
  } finally {
    creatingQueue.value = false
  }
}

// Handle file selection
const handleFileChange = (fileList: any) => {
  if (!fileList || fileList.length === 0) {
    currentFile.value = null
    return
  }
  // Take the last file in the fileList array
  const lastFile = fileList[fileList.length - 1]
  currentFile.value = lastFile && lastFile.file ? lastFile.file : null
}

// Upload file to OSS and submit queue item
const uploadAndSubmitItem = async () => {
  if (!queueId.value) {
    Message.warning('请先创建上传队列')
    return
  }

  if (!currentFile.value) {
    Message.warning('请选择要上传的图片')
    return
  }

  if (currentSequence.value <= 0) {
    Message.warning('请输入有效的序号')
    return
  }

  // Check if sequence already exists
  if (uploadedItems.some((item: UploadedItem) => item.sequence === currentSequence.value)) {
    Message.warning(`序号 ${currentSequence.value} 已存在，请使用其他序号`)
    return
  }

  uploadingFile.value = true

  try {
    // Upload file to OSS
    const fileId = await uploadFileToOss(currentFile.value)

    submittingItem.value = true

    // Submit queue item
    await submitQueueItem({
      queue_id: queueId.value,
      file_id: fileId,
      sequence: currentSequence.value
    })

    // Add to uploaded items
    uploadedItems.push({
      sequence: currentSequence.value,
      fileId
    })

    Message.success(`成功上传并提交序号 ${currentSequence.value} 的图片`)

    // Reset file and increment sequence
    currentFile.value = null
    currentSequence.value++
  } catch (error) {
    console.error('上传并提交图片失败:', error)
    Message.error('上传并提交图片失败')
  } finally {
    uploadingFile.value = false
    submittingItem.value = false
  }
}

// Reset queue
const resetQueue = () => {
  queueId.value = ''
  currentFile.value = null
  currentSequence.value = 1
  uploadedItems.length = 0
}

// Handle clipboard paste for images
const handlePaste = (event: ClipboardEvent) => {
  if (!queueId.value) {
    // Don't process paste events if no queue is created
    return
  }

  const clipboardData = event.clipboardData
  if (!clipboardData) {
    return
  }

  // Check if clipboard contains images
  const items = clipboardData.items
  let blob: File | null = null

  // Look for image items in the clipboard
  for (let i = 0; i < items.length; i++) {
    if (items[i].type.indexOf('image') !== -1) {
      blob = items[i].getAsFile()
      if (blob) break
    }
  }

  if (!blob) {
    return
  }

  // Create a File object from the blob
  const timestamp = new Date().getTime()
  const fileName = `pasted-image-${timestamp}.png`
  const file = new File([blob], fileName, { type: blob.type })

  // Update the current file
  currentFile.value = file

  Message.success('已从剪贴板粘贴图片')
}

// Setup and cleanup event listeners
onMounted(() => {
  document.addEventListener('paste', handlePaste)
})

onUnmounted(() => {
  document.removeEventListener('paste', handlePaste)
})
</script>

<template>
  <div class="container">
    <div class="page-container">
      <a-page-header
        title="队列上传"
        @back="() => $router.push('/')"
      />

      <a-alert
        v-if="!tokenStore.token"
        type="warning"
        class="mb-20"
      >
        <template #message>
          请先设置项目 Token 以进行 API 认证
        </template>
      </a-alert>

      <div class="card-container">
        <!-- Queue Creation Section -->
        <div class="section mb-20">
          <h3>创建上传队列</h3>

          <a-form
            :model="queueForm"
            layout="vertical"
          >
            <a-form-item
              field="total"
              label="总图片数量"
            >
              <a-input-number
                v-model="queueForm.total"
                :min="1"
                :max="100"
              />
            </a-form-item>

            <a-form-item>
              <a-button
                type="primary"
                :loading="creatingQueue"
                :disabled="!!queueId"
                @click="createQueue"
              >
                创建上传队列
              </a-button>

              <a-button
                v-if="queueId"
                type="outline"
                status="danger"
                class="ml-10"
                @click="resetQueue"
              >
                重置队列
              </a-button>
            </a-form-item>
          </a-form>

          <a-alert
            v-if="queueId"
            type="success"
            class="mt-10"
          >
            <template #message>
              队列已创建，ID: {{ queueId }}
            </template>
          </a-alert>
        </div>

        <!-- Queue Item Upload Section -->
        <div
          class="section"
          v-if="queueId"
        >
          <h3>上传队列项</h3>

          <a-form
            :model="{}"
            layout="vertical"
          >
            <a-form-item
              field="sequence"
              label="序号"
            >
              <a-input-number
                v-model="currentSequence"
                :min="1"
                :max="100"
              />
            </a-form-item>

            <a-form-item
              field="file"
              label="图片文件"
            >
              <a-upload
                action="/"
                :auto-upload="false"
                :file-list="fileList"
                :show-file-list="true"
                :limit="1"
                @change="handleFileChange"
              >
                <template #upload-button>
                  <a-button type="primary">
                    <template #icon>
                      <icon-plus />
                    </template>
                    选择图片
                  </a-button>
                </template>
              </a-upload>
              <div class="paste-tip mt-10">
                <a-tag color="blue">提示: 您也可以直接粘贴剪贴板中的图片 (Ctrl+V)</a-tag>
              </div>
            </a-form-item>

            <a-form-item>
              <a-button
                type="primary"
                :loading="uploadingFile || submittingItem"
                :disabled="!currentFile"
                @click="uploadAndSubmitItem"
              >
                上传并提交
              </a-button>
            </a-form-item>
          </a-form>
        </div>

        <!-- Uploaded Items Section -->
        <div
          class="section mt-20"
          v-if="uploadedItems.length > 0"
        >
          <h3>已上传项目</h3>

          <a-table
            :data="uploadedItems"
            :pagination="false"
          >
            <template #columns>
              <a-table-column
                title="序号"
                data-index="sequence"
              />
              <a-table-column
                title="文件ID"
                data-index="fileId"
              />
            </template>
          </a-table>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.section {
  border-bottom: 1px solid var(--color-border);
  padding-bottom: 20px;

  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  h3 {
    margin-top: 0;
    margin-bottom: 16px;
  }
}

.ml-10 {
  margin-left: 10px;
}

.mt-10 {
  margin-top: 10px;
}

.paste-tip {
  font-size: 14px;
  color: var(--color-text-3);
}
</style>
