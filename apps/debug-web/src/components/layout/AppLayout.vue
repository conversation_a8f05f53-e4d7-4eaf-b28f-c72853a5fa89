<script setup lang="ts">
import { ref, computed } from 'vue'
import { useTokenStore } from '@/store/token'

const tokenStore = useTokenStore()
const tokenInput = ref(tokenStore.token)
const showTokenInput = ref(false)

// Toggle token input visibility
const toggleTokenInput = () => {
  showTokenInput.value = !showTokenInput.value
}

// Save token
const saveToken = () => {
  tokenStore.setToken(tokenInput.value)
  showTokenInput.value = false
}

// Clear token
const clearToken = () => {
  tokenInput.value = ''
  tokenStore.clearToken()
  showTokenInput.value = false
}

// Computed property for token display
const displayToken = computed(() => {
  if (!tokenStore.token) return '未设置'
  
  // Show first 6 and last 4 characters
  const token = tokenStore.token
  if (token.length <= 10) return token
  
  return `${token.substring(0, 6)}...${token.substring(token.length - 4)}`
})
</script>

<template>
  <div class="app-layout">
    <!-- Header with token management -->
    <div class="app-header">
      <div class="logo">调试工具</div>
      
      <div class="token-section">
        <a-space>
          <a-tag size="medium" :color="tokenStore.token ? 'green' : 'red'">
            Token: {{ displayToken }}
          </a-tag>
          <a-button type="text" size="small" @click="toggleTokenInput">
            <template #icon>
              <icon-edit />
            </template>
            {{ tokenStore.token ? '修改' : '设置' }}
          </a-button>
        </a-space>
      </div>
    </div>
    
    <!-- Token input dialog -->
    <a-modal
      v-model:visible="showTokenInput"
      title="设置项目 Token"
      @cancel="showTokenInput = false"
      :footer="false"
      width="500px"
    >
      <a-form :model="{ token: tokenInput }">
        <a-form-item field="token" label="项目 Token">
          <a-input
            v-model="tokenInput"
            placeholder="请输入项目 Token"
            allow-clear
          />
          <template #help>
            Token 将用于 API 请求的 Bearer 认证
          </template>
        </a-form-item>
        
        <a-space>
          <a-button type="primary" @click="saveToken">保存</a-button>
          <a-button status="danger" @click="clearToken">清除</a-button>
          <a-button @click="showTokenInput = false">取消</a-button>
        </a-space>
      </a-form>
    </a-modal>
    
    <!-- Main content -->
    <div class="app-content">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped lang="scss">
.app-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  height: 60px;
  background-color: var(--color-bg-2);
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  
  .logo {
    font-size: 18px;
    font-weight: bold;
    color: var(--color-text-1);
  }
  
  .token-section {
    display: flex;
    align-items: center;
  }
}

.app-content {
  flex: 1;
  background-color: var(--color-bg-1);
}
</style>
