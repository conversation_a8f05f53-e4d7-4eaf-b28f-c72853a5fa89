import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { generateQRCode, getQRCodeInfo, type GetQRCodeResponse } from '@/api/qrcode'
import { useTokenStore } from './token'

// QR code status constants
export const QR_CODE_STATUS = {
  INIT: '',
  SCANNED: 'scanned',
  BIND: 'bind'
}

export const useQRCodeStore = defineStore('qrcode', () => {
  // Dependencies
  const tokenStore = useTokenStore()

  // State
  const qrCodeData = ref<GetQRCodeResponse | null>(null)
  const qrCodeStatus = ref<string>(QR_CODE_STATUS.INIT)
  const qrCodeError = ref<string | null>(null)
  const isPolling = ref<boolean>(false)
  const pollingInterval = ref<number | null>(null)
  const isLoading = ref<boolean>(false)

  // Computed
  const isExpired = computed(() => {
    if (!qrCodeData.value) return true
    return Date.now() > qrCodeData.value.expire_at * 1000
  })

  const remainingTime = computed(() => {
    if (!qrCodeData.value) return 0
    const remaining = qrCodeData.value.expire_at * 1000 - Date.now()
    return Math.max(0, Math.floor(remaining / 1000))
  })

  const formattedRemainingTime = computed(() => {
    const seconds = remainingTime.value
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  })

  // Actions
  async function generateNewQRCode() {
    try {
      isLoading.value = true
      qrCodeError.value = null
      qrCodeStatus.value = QR_CODE_STATUS.INIT

      // Stop any existing polling
      stopPolling()

      // Generate new QR code
      qrCodeData.value = await generateQRCode()

      // Start polling for status
      startPolling()

      return qrCodeData.value
    } catch (error: any) {
      qrCodeError.value = error.message || '生成二维码失败'
      throw error
    } finally {
      isLoading.value = false
    }
  }

  async function checkQRCodeStatus() {
    if (!qrCodeData.value || isExpired.value) {
      stopPolling()
      return
    }

    try {
      const response = await getQRCodeInfo(qrCodeData.value.qr_code_id)

      // Update status
      if (response.status) {
        qrCodeStatus.value = response.status
      }

      // If bound, save token and stop polling
      if (response.status === QR_CODE_STATUS.BIND && response.token) {
        tokenStore.setToken(response.token)
        stopPolling()
      }

      return response
    } catch (error: any) {
      console.error('Failed to check QR code status:', error)
      // Don't stop polling on error, just log it
    }
  }

  function startPolling() {
    if (isPolling.value) return

    isPolling.value = true

    // Poll every 5 seconds
    pollingInterval.value = window.setInterval(async () => {
      // If expired, stop polling
      if (isExpired.value) {
        stopPolling()
        return
      }

      await checkQRCodeStatus()
    }, 5000)
  }

  function stopPolling() {
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value)
      pollingInterval.value = null
    }

    isPolling.value = false
  }

  function reset() {
    stopPolling()
    qrCodeData.value = null
    qrCodeStatus.value = QR_CODE_STATUS.INIT
    qrCodeError.value = null
  }

  // Clean up on unmount
  function onUnmount() {
    stopPolling()
  }

  return {
    qrCodeData,
    qrCodeStatus,
    qrCodeError,
    isLoading,
    isPolling,
    isExpired,
    remainingTime,
    formattedRemainingTime,
    generateNewQRCode,
    checkQRCodeStatus,
    startPolling,
    stopPolling,
    reset,
    onUnmount
  }
})
