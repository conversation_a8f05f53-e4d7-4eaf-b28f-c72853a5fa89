import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

// Token storage key
const TOKEN_STORAGE_KEY = 'debug_app_token'

export const useTokenStore = defineStore('token', () => {
  // State
  const token = ref<string>(localStorage.getItem(TOKEN_STORAGE_KEY) || '')
  
  // Actions
  function setToken(newToken: string) {
    token.value = newToken
    localStorage.setItem(TOKEN_STORAGE_KEY, newToken)
  }
  
  function clearToken() {
    token.value = ''
    localStorage.removeItem(TOKEN_STORAGE_KEY)
  }
  
  // Getters
  const isAuthenticated = () => !!token.value
  
  // Watch for token changes from other tabs/windows
  watch(token, (newToken) => {
    if (newToken) {
      localStorage.setItem(TOKEN_STORAGE_KEY, newToken)
    } else {
      localStorage.removeItem(TOKEN_STORAGE_KEY)
    }
  })
  
  // Listen for storage events (for multi-tab support)
  window.addEventListener('storage', (event) => {
    if (event.key === TOKEN_STORAGE_KEY) {
      token.value = event.newValue || ''
    }
  })
  
  return {
    token,
    setToken,
    clearToken,
    isAuthenticated
  }
})
