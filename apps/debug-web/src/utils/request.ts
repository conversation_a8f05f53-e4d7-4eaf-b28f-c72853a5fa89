import axios, { type AxiosInstance, type AxiosRequestConfig } from 'axios'
import { type ApiResponse } from '@/api/type'
import { useTokenStore } from '@/store/token'
import { getAppVersion, getAppPlatform } from '@/utils/app-info'

class Request {
  private readonly axiosInstance: AxiosInstance

  constructor(config: AxiosRequestConfig) {
    this.axiosInstance = axios.create(config)
    this.axiosInstance.interceptors.request.use((config) => {
      const tokenStore = useTokenStore()
      console.log('token', tokenStore.token)
      if (tokenStore.token) {
        config.headers.Authorization = `Bearer ${tokenStore.token}`
      }

      // Add app version and platform headers
      config.headers['x-lu-version'] = getAppVersion()
      config.headers['x-lu-platform'] = getAppPlatform()

      return config
    })

    this.axiosInstance.interceptors.response.use(
      (response) => {
        const data: ApiResponse<any> = response.data
        if (response.status !== 200) {
          // Message.error({
          //   content: '服务器连接失败',
          // })
          return Promise.reject(data)
        }
        if (data.code !== 0) {
          // Message.error({
          //   content: data.message,
          // })
          return Promise.reject(data.message)
        }
        return data.data
      },
      (error) => {
        console.error('Request error:', error)
        // Message.error({
        //   content: '服务器连接失败',
        // })
        return Promise.reject(error)
      },
    )
  }

  async request<T = any>(config: AxiosRequestConfig): Promise<T> {
    return await this.axiosInstance(config)
  }

  async get<T = any>(url: string, params?: any): Promise<T> {
    return await this.axiosInstance({
      url,
      method: 'get',
      params,
    })
  }

  async post<T = any>(url: string, data?: any): Promise<T> {
    return await this.axiosInstance({
      url,
      method: 'post',
      data,
    })
  }

  async put<T = any>(url: string, data?: any): Promise<T> {
    return await this.axiosInstance({
      url,
      method: 'put',
      data,
    })
  }

  async patch<T = any>(url: string, data?: any): Promise<T> {
    return await this.axiosInstance({
      url,
      method: 'patch',
      data,
    })
  }

  async delete<T = any>(url: string, data?: any): Promise<T> {
    return await this.axiosInstance({
      url,
      method: 'delete',
      data,
    })
  }
}

export default new Request({})
