import axios from 'axios'
import { getOssToken } from '@/api/printer'

/**
 * Upload a file to OSS
 * @param file The file to upload
 * @returns The file ID (object key)
 */
export async function uploadFileToOss(file: File): Promise<string> {
  // Get file extension
  const ext = file.name.split('.').pop() || ''

  // Get OSS token
  const ossToken = await getOssToken(ext)

  // Create form data for upload
  const formData = new FormData()
  formData.append('key', ossToken.object_key)
  formData.append('policy', ossToken.policy)
  formData.append('OSSAccessKeyId', ossToken.access_key_id)
  formData.append('success_action_status', '200')
  formData.append('signature', ossToken.signature)
  formData.append('file', file)

  try {
    // Upload file to OSS
    const response = await axios.post(ossToken.host, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })

    if (response.status === 200) {
      console.log('上传阿里云成功', `${ossToken.host}/${ossToken.object_key}`, response)
      return ossToken.object_key
    } else {
      throw new Error(`Upload failed with status: ${response.status}`)
    }
  } catch (error) {
    console.error('上传失败', error)
    throw error
  }
}
